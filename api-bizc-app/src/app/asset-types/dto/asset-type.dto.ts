import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CategoryStatus, AssetTypeCategory } from '../../shared/types';
import { AuditFieldsDto } from '../../shared/dto/audit-fields.dto';

export class AssetTypeDto extends AuditFieldsDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Asset Type ID',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Business ID',
  })
  businessId: string;

  @ApiProperty({
    example: 'Computers',
    description: 'Asset type name',
  })
  name: string;

  @ApiPropertyOptional({
    example: 'Electronic computing devices and accessories',
    description: 'Asset type description',
  })
  description?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Parent asset type ID',
  })
  parentId?: string;

  @ApiProperty({
    enum: AssetTypeCategory,
    enumName: 'AssetTypeCategory',
    description: 'Asset type category',
    example: AssetTypeCategory.PHYSICAL,
  })
  category: AssetTypeCategory;

  @ApiProperty({
    example: CategoryStatus.ACTIVE,
    enum: CategoryStatus,
    enumName: 'CategoryStatus',
    description: 'Asset type status',
  })
  status: CategoryStatus;

  @ApiProperty({
    example: 5,
    description: 'Number of assets in this type',
  })
  assetsCount: number;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description:
      'Reference ID from source type (rental-item-category or vehicle-category)',
  })
  referenceId?: string;

  @ApiPropertyOptional({
    example: 'rental-item-category',
    enum: ['rental-item-category', 'vehicle-category'],
    description: 'Type of the source module',
  })
  referenceType?: 'rental-item-category' | 'vehicle-category';
}
