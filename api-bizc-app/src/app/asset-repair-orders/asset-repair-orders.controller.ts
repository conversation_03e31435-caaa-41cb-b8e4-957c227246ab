import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { AssetRepairOrdersService } from './asset-repair-orders.service';
import { CreateAssetRepairOrderDto } from './dto/create-asset-repair-order.dto';
import { UpdateAssetRepairOrderDto } from './dto/update-asset-repair-order.dto';
import { AssetRepairOrderDto } from './dto/asset-repair-order.dto';
import { AssetRepairOrderSlimDto } from './dto/asset-repair-order-slim.dto';
import { AssetRepairOrderIdResponseDto } from './dto/asset-repair-order-id-response.dto';
import { BulkCreateAssetRepairOrderDto } from './dto/bulk-create-asset-repair-order.dto';
import { BulkAssetRepairOrderIdsResponseDto } from './dto/bulk-asset-repair-order-ids-response.dto';
import { BulkDeleteAssetRepairOrderDto } from './dto/bulk-delete-asset-repair-order.dto';
import { BulkDeleteAssetRepairOrderResponseDto } from './dto/bulk-delete-asset-repair-order-response.dto';
import { PaginatedAssetRepairOrdersResponseDto } from './dto/paginated-asset-repair-orders-response.dto';
import { BulkUpdateAssetRepairOrderStatusDto } from './dto/bulk-update-asset-repair-order-status.dto';
import { BulkUpdateAssetRepairOrderStatusResponseDto } from './dto/bulk-update-asset-repair-order-status-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';
import type { AuthenticatedRequest } from '../shared/types';

@ApiTags('asset-repair-orders')
@Controller('asset-repair-orders')
@UseGuards(PermissionsGuard)
export class AssetRepairOrdersController {
  constructor(
    private readonly assetRepairOrdersService: AssetRepairOrdersService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_REPAIR_ORDER_CREATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new asset repair order with optional attachments',
  })
  @ApiBody({
    description: 'Asset repair order creation with optional file attachments',
    schema: {
      type: 'object',
      properties: {
        assetId: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'Asset ID being repaired',
        },
        title: {
          type: 'string',
          example: 'Motor Repair',
          description: 'Title of the repair order',
        },
        problemDescription: {
          type: 'string',
          example: 'Motor not starting properly',
          description: 'Description of the problem',
        },
        diagnosisNotes: {
          type: 'string',
          example: 'Electrical issue detected',
          description: 'Diagnosis notes',
        },
        repairSolution: {
          type: 'string',
          example: 'Replace faulty wiring',
          description: 'Repair solution description',
        },
        repairType: {
          type: 'string',
          enum: [
            'emergency',
            'scheduled',
            'corrective',
            'breakdown',
            'warranty',
          ],
          example: 'corrective',
          description: 'Type of repair',
        },
        status: {
          type: 'string',
          enum: [
            'reported',
            'diagnosed',
            'approved',
            'in_progress',
            'completed',
            'on_hold',
            'cancelled',
          ],
          example: 'reported',
          description: 'Current status of the repair order',
        },
        priority: {
          type: 'string',
          enum: ['low', 'normal', 'high', 'urgent', 'critical'],
          example: 'normal',
          description: 'Priority level of the repair',
        },
        targetCompletionDate: {
          type: 'string',
          format: 'date-time',
          example: '2023-01-15T00:00:00Z',
          description: 'Target completion date',
        },
        actualStartDate: {
          type: 'string',
          format: 'date-time',
          example: '2023-01-02T00:00:00Z',
          description: 'Actual start date of repair work',
        },
        completedAt: {
          type: 'string',
          format: 'date-time',
          example: '2023-01-10T00:00:00Z',
          description: 'Actual completion date',
        },
        completionNotes: {
          type: 'string',
          example: 'Repair completed successfully',
          description: 'Completion notes',
        },
        reportedBy: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'ID of staff member who reported the issue',
        },
        assignedTo: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'ID of staff member assigned to the repair',
        },
        supplierId: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'Supplier ID for external repairs',
        },
        isWarrantyRepair: {
          type: 'boolean',
          example: false,
          description: 'Whether this is a warranty repair',
        },
        warrantyClaimNumber: {
          type: 'string',
          example: 'WCN-2023-001',
          description: 'Warranty claim number',
        },
        expenseAccountId: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'Expense account ID',
        },
        paymentAccountId: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'Payment account ID',
        },
        paymentDate: {
          type: 'string',
          format: 'date',
          example: '2023-01-10',
          description: 'Payment date',
        },
        paymentMethodId: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'Payment method ID',
        },
        paymentReferenceNumber: {
          type: 'string',
          example: 'PAY-2023-001',
          description: 'Payment reference number',
        },
        amountType: {
          type: 'string',
          enum: ['Exclusive of Tax', 'Inclusive of Tax', 'Out Of Scope'],
          example: 'Exclusive of Tax',
          description: 'Amount calculation type',
        },
        subtotal: {
          type: 'string',
          example: '1000.00',
          description: 'Subtotal amount',
        },
        total: {
          type: 'string',
          example: '1100.00',
          description: 'Total amount',
        },
        taxId: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'Tax ID',
        },
        qualityCheckBy: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'Quality check performed by staff member ID',
        },
        attachments: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'File attachments for repair documentation',
        },
        mediaMetadata: {
          type: 'string',
          description:
            'JSON string containing media association metadata for uploaded attachments',
          example: JSON.stringify({
            attachmentDefaults: { purpose: 'attachment', isVisible: true },
            attachmentMetadata: {
              '0': { position: 0, purpose: 'attachment' },
              '1': { position: 1, purpose: 'attachment' },
            },
          }),
        },
      },
      required: [
        'assetId',
        'title',
        'problemDescription',
        'reportedBy',
        'expenseAccountId',
      ],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Asset repair order has been successfully created',
    type: AssetRepairOrderIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset repair order already exists',
  })
  create(
    @Request() req: AuthenticatedRequest,
    @Body() createAssetRepairOrderDto: CreateAssetRepairOrderDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @UploadedFiles() attachments?: Express.Multer.File[],
  ): Promise<AssetRepairOrderIdResponseDto> {
    return this.assetRepairOrdersService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAssetRepairOrderDto,
      attachments,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_REPAIR_ORDER_CREATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Bulk create asset repair orders with optional attachments',
  })
  @ApiBody({
    description:
      'Bulk asset repair order creation with optional attachment uploads. Attachments can be mapped to repair orders using field names like "attachment_0", "attachment_1", etc., or by using the assetRepairOrders array with attachmentIndex property.',
    schema: {
      type: 'object',
      properties: {
        assetRepairOrders: {
          type: 'string',
          description:
            'JSON string containing array of asset repair order objects. Each order can optionally include an "attachmentIndex" property to specify which attachment file to use.',
          example:
            '[{"assetId":"550e8400-e29b-41d4-a716-************","title":"Motor Repair","problemDescription":"Motor not starting","reportedBy":"550e8400-e29b-41d4-a716-************","expenseAccountId":"550e8400-e29b-41d4-a716-************","attachmentIndex":0}]',
        },
        attachments: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description:
            'Array of attachment files (optional). Attachments are mapped to repair orders using the attachmentIndex property in the assetRepairOrders array.',
        },
      },
      required: ['assetRepairOrders'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The asset repair orders have been successfully created',
    type: BulkAssetRepairOrderIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data, duplicate titles, or files',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset repair order titles already exist',
  })
  bulkCreate(
    @Request() req: AuthenticatedRequest,
    @Body() bulkCreateAssetRepairOrderDto: BulkCreateAssetRepairOrderDto,
    @UploadedFiles() attachments: Express.Multer.File[] = [],
  ): Promise<BulkAssetRepairOrderIdsResponseDto> {
    return this.assetRepairOrdersService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAssetRepairOrderDto.assetRepairOrders,
      attachments,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_REPAIR_ORDER_READ)
  @ApiOperation({
    summary: 'Get all asset repair orders for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'title',
    description: 'Filter by title (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status (comma-separated for multiple)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'priority',
    description: 'Filter by priority (comma-separated for multiple)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'repairType',
    description: 'Filter by repair type (comma-separated for multiple)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'assetName',
    description: 'Filter by asset name (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for multiple filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort by field:direction (e.g., reportedAt:desc, title:asc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset repair orders retrieved successfully',
    type: PaginatedAssetRepairOrdersResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: AuthenticatedRequest,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('title') title?: string,
    @Query('status') status?: string,
    @Query('priority') priority?: string,
    @Query('repairType') repairType?: string,
    @Query('assetName') assetName?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAssetRepairOrdersResponseDto> {
    return this.assetRepairOrdersService.findAllOptimized(
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      title,
      status,
      priority,
      repairType,
      assetName,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_REPAIR_ORDER_READ)
  @ApiOperation({ summary: 'Get all asset repair orders in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All asset repair orders returned successfully',
    type: [AssetRepairOrderSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(
    @Request() req: AuthenticatedRequest,
  ): Promise<AssetRepairOrderSlimDto[]> {
    return this.assetRepairOrdersService.findAllSlim(req.user.activeBusinessId);
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_REPAIR_ORDER_READ)
  @ApiOperation({ summary: 'Get a specific asset repair order by ID' })
  @ApiParam({
    name: 'id',
    description: 'Asset repair order ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset repair order found',
    type: AssetRepairOrderDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset repair order not found',
  })
  findOne(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
  ): Promise<AssetRepairOrderDto> {
    return this.assetRepairOrdersService.findOne(req.user.activeBusinessId, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_REPAIR_ORDER_UPDATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update an asset repair order with optional attachments',
  })
  @ApiParam({
    name: 'id',
    description: 'Asset repair order ID',
    type: String,
  })
  @ApiBody({
    description: 'Asset repair order update with optional file attachments',
    schema: {
      type: 'object',
      properties: {
        assetId: {
          type: 'string',
          format: 'uuid',
          description: 'Asset ID being repaired',
        },
        title: {
          type: 'string',
          description: 'Title of the repair order',
        },
        problemDescription: {
          type: 'string',
          description: 'Description of the problem',
        },
        diagnosisNotes: {
          type: 'string',
          description: 'Diagnosis notes',
        },
        repairSolution: {
          type: 'string',
          description: 'Repair solution description',
        },
        repairType: {
          type: 'string',
          enum: [
            'emergency',
            'scheduled',
            'corrective',
            'breakdown',
            'warranty',
          ],
          description: 'Type of repair',
        },
        status: {
          type: 'string',
          enum: [
            'reported',
            'diagnosed',
            'approved',
            'in_progress',
            'completed',
            'on_hold',
            'cancelled',
          ],
          description: 'Current status of the repair order',
        },
        priority: {
          type: 'string',
          enum: ['low', 'normal', 'high', 'urgent', 'critical'],
          description: 'Priority level of the repair',
        },
        targetCompletionDate: {
          type: 'string',
          format: 'date-time',
          description: 'Target completion date',
        },
        actualStartDate: {
          type: 'string',
          format: 'date-time',
          description: 'Actual start date of repair work',
        },
        completedAt: {
          type: 'string',
          format: 'date-time',
          description: 'Actual completion date',
        },
        completionNotes: {
          type: 'string',
          description: 'Completion notes',
        },
        reportedBy: {
          type: 'string',
          format: 'uuid',
          description: 'ID of staff member who reported the issue',
        },
        assignedTo: {
          type: 'string',
          format: 'uuid',
          description: 'ID of staff member assigned to the repair',
        },
        supplierId: {
          type: 'string',
          format: 'uuid',
          description: 'Supplier ID for external repairs',
        },
        isWarrantyRepair: {
          type: 'boolean',
          description: 'Whether this is a warranty repair',
        },
        warrantyClaimNumber: {
          type: 'string',
          description: 'Warranty claim number',
        },
        expenseAccountId: {
          type: 'string',
          format: 'uuid',
          description: 'Expense account ID',
        },
        paymentAccountId: {
          type: 'string',
          format: 'uuid',
          description: 'Payment account ID',
        },
        paymentDate: {
          type: 'string',
          format: 'date',
          description: 'Payment date',
        },
        paymentMethodId: {
          type: 'string',
          format: 'uuid',
          description: 'Payment method ID',
        },
        paymentReferenceNumber: {
          type: 'string',
          description: 'Payment reference number',
        },
        amountType: {
          type: 'string',
          enum: ['Exclusive of Tax', 'Inclusive of Tax', 'Out Of Scope'],
          description: 'Amount calculation type',
        },
        subtotal: {
          type: 'string',
          description: 'Subtotal amount',
        },
        total: {
          type: 'string',
          description: 'Total amount',
        },
        taxId: {
          type: 'string',
          format: 'uuid',
          description: 'Tax ID',
        },
        qualityCheckBy: {
          type: 'string',
          format: 'uuid',
          description: 'Quality check performed by staff member ID',
        },
        attachments: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'File attachments for repair documentation',
        },
        mediaMetadata: {
          type: 'string',
          description:
            'JSON string containing media association metadata for uploaded attachments',
          example: JSON.stringify({
            attachmentDefaults: { purpose: 'attachment', isVisible: true },
            attachmentMetadata: {
              '0': { position: 0, purpose: 'attachment' },
              '1': { position: 1, purpose: 'attachment' },
            },
          }),
        },
        deleteMediaIds: {
          type: 'array',
          items: { type: 'string', format: 'uuid' },
          description: 'Media association IDs to delete',
          example: [
            '550e8400-e29b-41d4-a716-************',
            '550e8400-e29b-41d4-a716-************',
          ],
        },
        updateMediaAssociations: {
          type: 'array',
          description: 'Existing media associations to update',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              position: { type: 'number' },
              isPrimary: { type: 'boolean' },
              isVisible: { type: 'boolean' },
              metadata: { type: 'object' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Asset repair order has been successfully updated',
    type: AssetRepairOrderIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset repair order not found',
  })
  update(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() updateAssetRepairOrderDto: UpdateAssetRepairOrderDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @UploadedFiles() attachments?: Express.Multer.File[],
  ): Promise<AssetRepairOrderIdResponseDto> {
    return this.assetRepairOrdersService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAssetRepairOrderDto,
      attachments,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_REPAIR_ORDER_DELETE)
  @ApiOperation({ summary: 'Delete an asset repair order' })
  @ApiParam({
    name: 'id',
    description: 'Asset repair order ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset repair order has been successfully deleted',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true,
        },
        message: {
          type: 'string',
          example:
            'Asset repair order with ID 550e8400-e29b-41d4-a716-************ has been deleted',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset repair order not found',
  })
  remove(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<{ success: boolean; message: string }> {
    return this.assetRepairOrdersService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_REPAIR_ORDER_DELETE)
  @ApiOperation({ summary: 'Bulk delete asset repair orders' })
  @ApiBody({
    description: 'Array of asset repair order IDs to delete',
    type: BulkDeleteAssetRepairOrderDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset repair orders have been successfully deleted',
    type: BulkDeleteAssetRepairOrderResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No IDs provided or invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No valid asset repair orders found for deletion',
  })
  bulkDelete(
    @Request() req: AuthenticatedRequest,
    @Body() bulkDeleteAssetRepairOrderDto: BulkDeleteAssetRepairOrderDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAssetRepairOrderResponseDto> {
    return this.assetRepairOrdersService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAssetRepairOrderDto.assetRepairOrderIds,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_REPAIR_ORDER_UPDATE)
  @ApiOperation({ summary: 'Bulk update asset repair order status' })
  @ApiBody({
    description: 'Array of asset repair order IDs and status to update',
    type: BulkUpdateAssetRepairOrderStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset repair order status has been successfully updated',
    type: BulkUpdateAssetRepairOrderStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset repair orders not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req: AuthenticatedRequest,
    @Body() bulkUpdateStatusDto: BulkUpdateAssetRepairOrderStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAssetRepairOrderStatusResponseDto> {
    const result =
      await this.assetRepairOrdersService.bulkUpdateAssetRepairOrderStatus(
        req.user.id,
        req.user.activeBusinessId,
        bulkUpdateStatusDto.assetRepairOrderIds,
        bulkUpdateStatusDto.status,
        metadata,
      );

    return {
      updatedCount: result.updated,
      updatedIds: result.updatedIds,
      status: bulkUpdateStatusDto.status,
      message: `Successfully updated status for ${result.updated} asset repair orders`,
    };
  }
}
