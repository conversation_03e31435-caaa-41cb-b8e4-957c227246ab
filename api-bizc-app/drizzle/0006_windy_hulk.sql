CREATE TYPE "public"."damage_category" AS ENUM('physical', 'electrical', 'mechanical', 'software', 'environmental');--> statement-breakpoint
CREATE TYPE "public"."damage_severity" AS ENUM('minor', 'moderate', 'severe', 'critical');--> statement-breakpoint
CREATE TYPE "public"."operational_impact" AS ENUM('none', 'partial', 'complete');--> statement-breakpoint
CREATE TYPE "public"."responsibility_type" AS ENUM('user_error', 'accident', 'malfunction', 'vandalism', 'natural_disaster');--> statement-breakpoint
ALTER TABLE "asset_damage" ALTER COLUMN "damage_category" SET DATA TYPE "public"."damage_category" USING "damage_category"::"public"."damage_category";--> statement-breakpoint
ALTER TABLE "asset_damage" ALTER COLUMN "severity" SET DATA TYPE "public"."damage_severity" USING "severity"::"public"."damage_severity";--> statement-breakpoint
ALTER TABLE "asset_damage" ALTER COLUMN "repair_status" SET DEFAULT 'reported'::"public"."repair_status";--> statement-breakpoint
ALTER TABLE "asset_damage" ALTER COLUMN "repair_status" SET DATA TYPE "public"."repair_status" USING "repair_status"::"public"."repair_status";--> statement-breakpoint
ALTER TABLE "asset_damage" ALTER COLUMN "priority" SET DEFAULT 'medium'::"public"."priority";--> statement-breakpoint
ALTER TABLE "asset_damage" ALTER COLUMN "priority" SET DATA TYPE "public"."priority" USING "priority"::"public"."priority";--> statement-breakpoint
ALTER TABLE "asset_damage" ALTER COLUMN "responsibility_type" SET DATA TYPE "public"."responsibility_type" USING "responsibility_type"::"public"."responsibility_type";--> statement-breakpoint
ALTER TABLE "asset_damage" ALTER COLUMN "operational_impact" SET DATA TYPE "public"."operational_impact" USING "operational_impact"::"public"."operational_impact";