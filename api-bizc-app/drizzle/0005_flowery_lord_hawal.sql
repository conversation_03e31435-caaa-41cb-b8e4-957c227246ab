ALTER TABLE "asset_damage" ADD COLUMN "damage_category" text NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "severity" text NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "reported_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "occurred_at" timestamp NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "location_details" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "coordinates" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_status" text DEFAULT 'reported' NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "priority" text DEFAULT 'medium' NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_due_date" date;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "responsible_party_id" uuid;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "responsibility_type" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "is_insurance_claim" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "insurance_claim_number" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "is_warranty_covered" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "warranty_claim_number" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "estimated_repair_cost" numeric(12, 2);--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "labor_cost" numeric(12, 2);--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "parts_cost" numeric(12, 2);--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "attachment_ids" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "before_photo_ids" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "after_photo_ids" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_vendor_id" uuid;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_invoice_number" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "operational_impact" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "affected_functions" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "downtime" numeric(10, 2);--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "approved_by" uuid;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "approved_at" timestamp;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "approval_notes" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD CONSTRAINT "asset_damage_responsible_party_id_staff_members_id_fk" FOREIGN KEY ("responsible_party_id") REFERENCES "public"."staff_members"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD CONSTRAINT "asset_damage_repair_vendor_id_suppliers_id_fk" FOREIGN KEY ("repair_vendor_id") REFERENCES "public"."suppliers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD CONSTRAINT "asset_damage_approved_by_users_id_fk" FOREIGN KEY ("approved_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "asset_damage_category_index" ON "asset_damage" USING btree ("damage_category");--> statement-breakpoint
CREATE INDEX "asset_damage_severity_index" ON "asset_damage" USING btree ("severity");--> statement-breakpoint
CREATE INDEX "asset_damage_repair_status_index" ON "asset_damage" USING btree ("repair_status");--> statement-breakpoint
CREATE INDEX "asset_damage_priority_index" ON "asset_damage" USING btree ("priority");--> statement-breakpoint
CREATE INDEX "asset_damage_responsible_party_index" ON "asset_damage" USING btree ("responsible_party_id");--> statement-breakpoint
CREATE INDEX "asset_damage_repair_vendor_index" ON "asset_damage" USING btree ("repair_vendor_id");--> statement-breakpoint
CREATE INDEX "asset_damage_approved_by_index" ON "asset_damage" USING btree ("approved_by");--> statement-breakpoint
CREATE INDEX "asset_damage_severity_priority_index" ON "asset_damage" USING btree ("severity","priority","is_fixed") WHERE "asset_damage"."is_deleted" = false;--> statement-breakpoint
CREATE INDEX "asset_damage_repair_status_business_index" ON "asset_damage" USING btree ("repair_status","business_id") WHERE "asset_damage"."is_deleted" = false;--> statement-breakpoint
CREATE INDEX "asset_damage_business_priority_index" ON "asset_damage" USING btree ("business_id","priority","repair_status") WHERE "asset_damage"."is_deleted" = false;