import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AllowanceCalculationMethod } from '../../shared/types';
import { AuditFieldsDto } from '../../shared/dto/audit-fields.dto';

export class AllowanceTypeDto extends AuditFieldsDto {
  @ApiProperty({ description: 'Allowance type ID' })
  id: string;

  @ApiProperty({ description: 'Business ID' })
  businessId: string;

  @ApiProperty({ description: 'Allowance name' })
  allowanceName: string;

  @ApiProperty({ description: 'Allowance code' })
  allowanceCode: string;

  @ApiProperty({
    description: 'Calculation method',
    enum: AllowanceCalculationMethod,
  })
  calculationMethod: AllowanceCalculationMethod;

  @ApiProperty({ description: 'Is taxable' })
  isTaxable: boolean;

  @ApiPropertyOptional({ description: 'Tax rate ID' })
  taxRateId?: string;

  @ApiProperty({ description: 'Is active' })
  isActive: boolean;

  @ApiProperty({ description: 'Is EPF/ETF eligible' })
  isEPFETFEligible: boolean;

  @ApiPropertyOptional({ description: 'Amount' })
  amount?: number;

  @ApiPropertyOptional({ description: 'Description' })
  description?: string;
}
