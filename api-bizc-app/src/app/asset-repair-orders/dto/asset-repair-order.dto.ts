import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RepairStatus, RepairPriority, RepairType } from '../../shared/types';
import { AmountType } from '../../drizzle/schema/expenses.schema';

export class AssetRepairOrderDto {
  @ApiProperty({ description: 'Unique identifier for the asset repair order' })
  id: string;

  @ApiProperty({ description: 'Business ID the repair order belongs to' })
  businessId: string;

  @ApiProperty({ description: 'Asset ID being repaired' })
  assetId: string;

  @ApiProperty({ description: 'Repair order number' })
  repairOrderNumber: string;

  @ApiProperty({ description: 'Title of the repair order' })
  title: string;

  @ApiProperty({ description: 'Description of the problem' })
  problemDescription: string;

  @ApiPropertyOptional({
    description: 'Diagnosis notes',
    nullable: true,
  })
  diagnosisNotes?: string;

  @ApiPropertyOptional({
    description: 'Repair solution description',
    nullable: true,
  })
  repairSolution?: string;

  @ApiProperty({
    description: 'Type of repair',
    enum: RepairType,
    enumName: 'RepairType',
    example: RepairType.CORRECTIVE,
  })
  repairType: RepairType;

  @ApiProperty({
    description: 'Current status of the repair order',
    enum: RepairStatus,
    enumName: 'RepairStatus',
    example: RepairStatus.REPORTED,
  })
  status: RepairStatus;

  @ApiProperty({
    description: 'Priority level of the repair',
    enum: RepairPriority,
    enumName: 'RepairPriority',
    example: RepairPriority.NORMAL,
  })
  priority: RepairPriority;

  @ApiProperty({
    description: 'When the issue was reported',
    example: '2023-01-01T00:00:00Z',
  })
  reportedAt: Date;

  @ApiPropertyOptional({
    description: 'Target completion date',
    example: '2023-01-15T00:00:00Z',
    nullable: true,
  })
  targetCompletionDate?: Date;

  @ApiPropertyOptional({
    description: 'Actual start date of repair work',
    example: '2023-01-02T00:00:00Z',
    nullable: true,
  })
  actualStartDate?: Date;

  @ApiPropertyOptional({
    description: 'Actual completion date',
    example: '2023-01-10T00:00:00Z',
    nullable: true,
  })
  completedAt?: Date;

  @ApiPropertyOptional({
    description: 'Completion notes',
    nullable: true,
  })
  completionNotes?: string;

  @ApiProperty({ description: 'ID of staff member who reported the issue' })
  reportedBy: string;

  @ApiProperty({ description: 'Name of staff member who reported the issue' })
  reportedByName: string;

  @ApiPropertyOptional({
    description: 'ID of staff member assigned to the repair',
    nullable: true,
  })
  assignedTo?: string;

  @ApiPropertyOptional({
    description: 'Name of staff member assigned to the repair',
    nullable: true,
  })
  assignedToName?: string;

  @ApiPropertyOptional({
    description: 'Supplier ID for external repairs',
    nullable: true,
  })
  supplierId?: string;

  @ApiPropertyOptional({
    description: 'Supplier name for external repairs',
    nullable: true,
  })
  supplierName?: string;

  @ApiProperty({
    description: 'Whether this is a warranty repair',
    example: false,
  })
  isWarrantyRepair: boolean;

  @ApiPropertyOptional({
    description: 'Warranty claim number',
    nullable: true,
  })
  warrantyClaimNumber?: string;

  @ApiProperty({ description: 'Expense account ID' })
  expenseAccountId: string;

  @ApiProperty({ description: 'Expense account name' })
  expenseAccountName: string;

  @ApiPropertyOptional({
    description: 'Payment account ID',
    nullable: true,
  })
  paymentAccountId?: string;

  @ApiPropertyOptional({
    description: 'Payment account name',
    nullable: true,
  })
  paymentAccountName?: string;

  @ApiPropertyOptional({
    description: 'Payment date',
    example: '2023-01-10',
    nullable: true,
  })
  paymentDate?: string;

  @ApiPropertyOptional({
    description: 'Payment method ID',
    nullable: true,
  })
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'Payment method name',
    nullable: true,
  })
  paymentMethodName?: string;

  @ApiPropertyOptional({
    description: 'Payment reference number',
    nullable: true,
  })
  paymentReferenceNumber?: string;

  @ApiProperty({
    description: 'Amount calculation type',
    enum: AmountType,
    enumName: 'AmountType',
    example: AmountType.EXCLUSIVE_OF_TAX,
  })
  amountType: AmountType;

  @ApiProperty({
    description: 'Subtotal amount',
    example: '1000.00',
  })
  subtotal: string;

  @ApiProperty({
    description: 'Total amount',
    example: '1100.00',
  })
  total: string;

  @ApiPropertyOptional({
    description: 'Tax ID',
    nullable: true,
  })
  taxId?: string;

  @ApiPropertyOptional({
    description: 'Tax name',
    nullable: true,
  })
  taxName?: string;

  @ApiPropertyOptional({
    description: 'Quality check performed by staff member ID',
    nullable: true,
  })
  qualityCheckBy?: string;

  @ApiPropertyOptional({
    description: 'Quality check performed by staff member name',
    nullable: true,
  })
  qualityCheckByName?: string;

  @ApiProperty({ description: 'Asset name' })
  assetName: string;

  @ApiProperty({ description: 'Asset code' })
  assetCode: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the repair order',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the repair order',
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
