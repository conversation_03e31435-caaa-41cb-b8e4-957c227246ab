import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  AssetsTransactionType,
  AssetTransactionStatus,
} from '../../shared/types';

export class AssetTransactionListDto {
  @ApiProperty({
    description: 'Asset transaction ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiPropertyOptional({
    description: 'Asset name',
    example: 'Laptop Dell XPS 13',
    nullable: true,
  })
  assetName?: string;

  @ApiProperty({
    description: 'Transaction type',
    enum: AssetsTransactionType,
    enumName: 'AssetsTransactionType',
    example: AssetsTransactionType.ALLOCATION,
  })
  transactionType: AssetsTransactionType;

  @ApiProperty({
    description: 'Transaction status',
    enum: AssetTransactionStatus,
    enumName: 'AssetTransactionStatus',
    example: AssetTransactionStatus.COMPLETED,
  })
  status: AssetTransactionStatus;

  @ApiProperty({
    description: 'Reference number for the transaction',
    example: 'TXN-2024-001',
  })
  refNo: string;

  @ApiPropertyOptional({
    description: 'Receiver staff member name',
    example: 'John Doe',
    nullable: true,
  })
  receiverName?: string;

  @ApiPropertyOptional({
    description: 'Source location name',
    example: 'Main Warehouse',
    nullable: true,
  })
  fromLocationName?: string;

  @ApiPropertyOptional({
    description: 'Destination location name',
    example: 'Branch Office',
    nullable: true,
  })
  toLocationName?: string;

  @ApiProperty({
    description: 'Date and time of the transaction',
    example: '2024-01-15T10:30:00Z',
  })
  transactionDatetime: Date;

  @ApiPropertyOptional({
    description:
      'Date until which the asset is allocated (for allocation transactions)',
    example: '2024-12-31',
    nullable: true,
  })
  allocatedUpto?: Date;

  @ApiPropertyOptional({
    description: 'Return due date for the asset',
    example: '2024-06-30',
    nullable: true,
  })
  returnDueDate?: Date;

  @ApiPropertyOptional({
    description: 'Actual return date of the asset',
    example: '2024-06-28',
    nullable: true,
  })
  actualReturnDate?: Date;

  @ApiPropertyOptional({
    description: 'Asset condition before the transaction',
    example: 'good',
    nullable: true,
  })
  conditionBefore?: string;

  @ApiPropertyOptional({
    description: 'Asset condition after the transaction',
    example: 'excellent',
    nullable: true,
  })
  conditionAfter?: string;

  @ApiPropertyOptional({
    description: 'Additional notes for the transaction',
    example: 'Asset transferred for project requirements',
    nullable: true,
  })
  notes?: string;
}
