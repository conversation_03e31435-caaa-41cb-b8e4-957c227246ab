import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  AppointmentStatus,
  BookingSource,
} from '../../drizzle/schema/appointments.schema';
import { AuditFieldsDto } from '../../shared/dto/audit-fields.dto';

export class AppointmentCustomerDto {
  @ApiProperty({ description: 'Customer ID' })
  id: string;

  @ApiProperty({ description: 'Customer name' })
  name: string;

  @ApiPropertyOptional({ description: 'Customer email' })
  email?: string;

  @ApiPropertyOptional({ description: 'Customer phone' })
  phone?: string;
}

export class AppointmentLocationDto {
  @ApiProperty({ description: 'Location ID' })
  id: string;

  @ApiProperty({ description: 'Location name' })
  name: string;

  @ApiProperty({ description: 'Location code' })
  code: string;
}

export class AppointmentTimeSlotDto {
  @ApiProperty({ description: 'Time slot ID' })
  id: string;

  @ApiProperty({ description: 'Time slot name' })
  name: string;

  @ApiProperty({ description: 'Start time' })
  startTime: string;

  @ApiProperty({ description: 'End time' })
  endTime: string;
}

export class AppointmentServiceDto {
  @ApiProperty({ description: 'Service ID' })
  id: string;

  @ApiProperty({ description: 'Service name' })
  name: string;

  @ApiPropertyOptional({ description: 'Service price' })
  price?: string;
}

export class AppointmentStaffDto {
  @ApiProperty({ description: 'Staff member ID' })
  id: string;

  @ApiProperty({ description: 'Staff member name' })
  name: string;

  @ApiPropertyOptional({ description: 'Staff member email' })
  email?: string;
}

export class AppointmentDto extends AuditFieldsDto {
  @ApiProperty({ description: 'Unique identifier for the appointment' })
  id: string;

  @ApiProperty({
    description: 'Unique appointment number',
    example: 'APT-001',
  })
  appointmentNumber: string;

  @ApiPropertyOptional({
    description: 'Customer information',
    type: AppointmentCustomerDto,
  })
  customer?: AppointmentCustomerDto;

  @ApiPropertyOptional({
    description: 'Location information',
    type: AppointmentLocationDto,
  })
  location?: AppointmentLocationDto;

  @ApiPropertyOptional({
    description: 'Time slot information',
    type: AppointmentTimeSlotDto,
  })
  timeSlot?: AppointmentTimeSlotDto;

  @ApiProperty({
    description: 'Appointment status',
    enum: AppointmentStatus,
    enumName: 'AppointmentStatus',
    example: AppointmentStatus.SCHEDULED,
  })
  status: AppointmentStatus;

  @ApiProperty({
    description: 'Appointment date and time',
    example: '2024-01-15T10:00:00Z',
  })
  appointmentDate: Date;

  @ApiPropertyOptional({
    description: 'Additional notes for the appointment',
  })
  notes?: string;

  @ApiPropertyOptional({
    description: 'Reason for cancellation (if applicable)',
  })
  cancellationReason?: string;

  @ApiPropertyOptional({
    description: 'When the appointment was confirmed',
    example: '2024-01-14T15:30:00Z',
  })
  confirmedAt?: Date;

  @ApiPropertyOptional({
    description: 'When the reminder was sent',
    example: '2024-01-14T09:00:00Z',
  })
  reminderSentAt?: Date;

  @ApiPropertyOptional({
    description: 'Source of the booking',
    enum: BookingSource,
    enumName: 'BookingSource',
    example: BookingSource.ONLINE,
  })
  bookingSource?: BookingSource;

  @ApiPropertyOptional({
    description: 'Total estimated cost for the appointment',
    example: '150.00',
  })
  totalEstimatedCost?: string;

  @ApiPropertyOptional({
    description: 'Deposit amount required',
    example: '50.00',
  })
  depositAmount?: string;

  @ApiPropertyOptional({
    description: 'Whether the deposit has been paid',
    example: false,
  })
  depositPaid?: boolean;

  @ApiProperty({
    description: 'Services associated with this appointment',
    type: [AppointmentServiceDto],
  })
  services: AppointmentServiceDto[];

  @ApiProperty({
    description: 'Staff members assigned to this appointment',
    type: [AppointmentStaffDto],
  })
  staff: AppointmentStaffDto[];
}
