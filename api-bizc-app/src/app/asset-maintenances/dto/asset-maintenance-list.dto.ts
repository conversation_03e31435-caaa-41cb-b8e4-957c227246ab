import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { MaintenanceStatus, MaintenancePriority } from '../../shared/types';
import { AmountType } from '../../drizzle/schema/expenses.schema';

export class AssetMaintenanceListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Asset maintenance ID',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440002',
    description: 'Asset ID',
  })
  assetId: string;

  @ApiPropertyOptional({
    example: 'WO-2024-001',
    description: 'Work order number',
    nullable: true,
  })
  workOrderNumber?: string;

  @ApiProperty({
    example: 'Engine Oil Change',
    description: 'Maintenance title',
  })
  title: string;

  @ApiPropertyOptional({
    example: 'Regular engine oil change and filter replacement',
    description: 'Maintenance description',
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    example: MaintenanceStatus.PENDING,
    enum: MaintenanceStatus,
    enumName: 'MaintenanceStatus',
    description: 'Maintenance status',
  })
  status: MaintenanceStatus;

  @ApiProperty({
    example: MaintenancePriority.NORMAL,
    enum: MaintenancePriority,
    enumName: 'MaintenancePriority',
    description: 'Maintenance priority',
  })
  priority: MaintenancePriority;

  @ApiPropertyOptional({
    example: 'ABC Auto Services',
    description: 'Supplier name',
    nullable: true,
  })
  supplierName?: string;

  @ApiPropertyOptional({
    example: 'John Doe',
    description: 'Name of staff member assigned to maintenance',
    nullable: true,
  })
  assignedToName?: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of staff member for quality check',
    nullable: true,
  })
  qualityCheckByName?: string;

  @ApiPropertyOptional({
    example: '2024-01-15T10:30:00Z',
    description: 'Completion timestamp',
    nullable: true,
  })
  completedAt?: string;

  @ApiProperty({
    example: '2024-01-15',
    description: 'Payment date',
  })
  paymentDate: string;

  @ApiPropertyOptional({
    example: 'Credit Card',
    description: 'Payment method name',
    nullable: true,
  })
  paymentMethodName?: string;

  @ApiPropertyOptional({
    example: 'REF-2024-001',
    description: 'Payment reference number',
    nullable: true,
  })
  paymentReferenceNumber?: string;

  @ApiProperty({
    example: AmountType.EXCLUSIVE_OF_TAX,
    enum: AmountType,
    enumName: 'AmountType',
    description: 'Amount type',
  })
  amountType: AmountType;

  @ApiProperty({
    example: '100.00',
    description: 'Subtotal amount',
  })
  subtotal: string;

  @ApiProperty({
    example: '115.00',
    description: 'Total amount',
  })
  total: string;

  @ApiPropertyOptional({
    example: 'VAT 15%',
    description: 'Tax name',
    nullable: true,
  })
  taxName?: string;

  @ApiPropertyOptional({
    example: 'Asset Name',
    description: 'Asset name',
    nullable: true,
  })
  assetName?: string;

  @ApiPropertyOptional({
    example: 'ASSET-001',
    description: 'Asset code',
    nullable: true,
  })
  assetCode?: string;
}
