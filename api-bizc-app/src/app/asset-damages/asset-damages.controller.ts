import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { AssetDamagesService } from './asset-damages.service';
import { CreateAssetDamageDto } from './dto/create-asset-damage.dto';
import { UpdateAssetDamageDto } from './dto/update-asset-damage.dto';
import { AssetDamageDto } from './dto/asset-damage.dto';

import { AssetDamageIdResponseDto } from './dto/asset-damage-id-response.dto';
import { BulkDeleteAssetDamageDto } from './dto/bulk-delete-asset-damage.dto';
import { BulkDeleteAssetDamageResponseDto } from './dto/bulk-delete-asset-damage-response.dto';
import { PaginatedAssetDamagesResponseDto } from './dto/paginated-asset-damages-response.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';
import type { AuthenticatedRequest } from '../shared/types';

@ApiTags('asset-damages')
@Controller('asset-damages')
@UseGuards(PermissionsGuard)
export class AssetDamagesController {
  constructor(private readonly assetDamagesService: AssetDamagesService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_DAMAGE_CREATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new asset damage record with optional attachments',
  })
  @ApiBody({
    description: 'Asset damage creation data with optional attachment files',
    type: CreateAssetDamageDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Asset damage record created successfully',
    type: AssetDamageIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  create(
    @Request() req: AuthenticatedRequest,
    @Body() createAssetDamageDto: CreateAssetDamageDto,
    @UploadedFiles() attachments?: Express.Multer.File[],
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<AssetDamageIdResponseDto> {
    return this.assetDamagesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAssetDamageDto,
      attachments,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_DAMAGE_READ)
  @ApiOperation({
    summary: 'Get all vehicle damage records for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date filter (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date filter (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'assetId',
    description: 'Filter by asset ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'damageType',
    description: 'Filter by damage type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'isRepaired',
    description: 'Filter by repaired status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort order (e.g., "incidentAt:desc")',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicle damage records retrieved successfully',
    type: PaginatedAssetDamagesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: AuthenticatedRequest,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('assetId') assetId?: string,
    @Query('damageType') damageType?: string,
    @Query('isRepaired') isRepaired?: string,
    @Query('sort') sort?: string,
  ): Promise<PaginatedAssetDamagesResponseDto> {
    return this.assetDamagesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page, 10) : 1,
      limit ? parseInt(limit, 10) : 10,
      from,
      to,
      assetId,
      damageType,
      isRepaired,
      sort,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_DAMAGE_READ)
  @ApiOperation({ summary: 'Get an asset damage record by ID' })
  @ApiResponse({
    status: 200,
    description: 'Asset damage record retrieved successfully',
    type: AssetDamageDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Asset damage record not found',
  })
  findOne(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
  ): Promise<AssetDamageDto> {
    return this.assetDamagesService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_DAMAGE_UPDATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update an asset damage record with optional attachments',
  })
  @ApiBody({
    description: 'Asset damage update data with optional attachment files',
    type: UpdateAssetDamageDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset damage record updated successfully',
    type: AssetDamageIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Asset damage record not found',
  })
  update(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() updateAssetDamageDto: UpdateAssetDamageDto,
    @UploadedFiles() attachments?: Express.Multer.File[],
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<AssetDamageIdResponseDto> {
    return this.assetDamagesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAssetDamageDto,
      attachments,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_DAMAGE_DELETE)
  @ApiOperation({ summary: 'Bulk delete asset damage records' })
  @ApiResponse({
    status: 200,
    description: 'Asset damage records deleted successfully',
    type: BulkDeleteAssetDamageResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req: AuthenticatedRequest,
    @Body() bulkDeleteAssetDamageDto: BulkDeleteAssetDamageDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAssetDamageResponseDto> {
    return this.assetDamagesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAssetDamageDto.ids,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_DAMAGE_DELETE)
  @ApiOperation({ summary: 'Delete an asset damage record' })
  @ApiResponse({
    status: 200,
    description: 'Asset damage record deleted successfully',
    type: AssetDamageIdResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Asset damage record not found',
  })
  remove(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AssetDamageIdResponseDto> {
    return this.assetDamagesService.removeAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
