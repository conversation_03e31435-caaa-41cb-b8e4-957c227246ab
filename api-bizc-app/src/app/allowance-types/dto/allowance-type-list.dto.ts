import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AllowanceCalculationMethod } from '../../shared/types';

export class AllowanceTypeListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Allowance type ID',
  })
  id: string;

  @ApiProperty({
    example: 'Transport Allowance',
    description: 'Allowance name',
  })
  allowanceName: string;

  @ApiProperty({
    example: 'TA001',
    description: 'Allowance code',
  })
  allowanceCode: string;

  @ApiProperty({
    example: AllowanceCalculationMethod.FIXED,
    enum: AllowanceCalculationMethod,
    enumName: 'AllowanceCalculationMethod',
    description: 'Calculation method',
  })
  calculationMethod: AllowanceCalculationMethod;

  @ApiProperty({
    example: true,
    description: 'Whether the allowance is taxable',
  })
  isTaxable: boolean;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440001',
    description: 'Tax rate ID',
    nullable: true,
  })
  taxRateId?: string;

  @ApiPropertyOptional({
    example: 1000,
    description: 'Amount',
    nullable: true,
  })
  amount?: number;

  @ApiProperty({
    example: true,
    description: 'Whether the allowance is active',
  })
  isActive: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether the allowance is eligible for EPF/ETF',
  })
  isEPFETFEligible: boolean;

  @ApiPropertyOptional({
    example: 'Monthly transport allowance for employees',
    description: 'Allowance description',
    nullable: true,
  })
  description?: string;
}
