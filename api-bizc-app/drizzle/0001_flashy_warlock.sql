ALTER TABLE "business_user_locations" DROP CONSTRAINT "business_user_locations_deleted_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "business_user_locations" ADD COLUMN "is_deleted" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "business_user_locations" ADD COLUMN "business_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "business_user_locations" ADD CONSTRAINT "business_user_locations_business_id_business_id_fk" FOREIGN KEY ("business_id") REFERENCES "public"."business"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "business_user_locations_id_index" ON "business_user_locations" USING btree ("id");--> statement-breakpoint
CREATE INDEX "business_user_locations_business_id_index" ON "business_user_locations" USING btree ("business_id");--> statement-breakpoint
CREATE INDEX "business_user_locations_created_by_index" ON "business_user_locations" USING btree ("created_by");--> statement-breakpoint
CREATE INDEX "business_user_locations_updated_by_index" ON "business_user_locations" USING btree ("updated_by");--> statement-breakpoint
CREATE INDEX "business_user_locations_created_at_index" ON "business_user_locations" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "business_user_locations_updated_at_index" ON "business_user_locations" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX "business_user_locations_is_deleted_index" ON "business_user_locations" USING btree ("is_deleted");--> statement-breakpoint
ALTER TABLE "business_user_locations" DROP COLUMN "deleted_by";--> statement-breakpoint
ALTER TABLE "business_user_locations" DROP COLUMN "deleted_at";--> statement-breakpoint
ALTER TABLE "business_users" DROP COLUMN "is_active_business";