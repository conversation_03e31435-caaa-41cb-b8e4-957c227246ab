import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { MaintenanceStatus, MaintenancePriority } from '../../shared/types';
import { AmountType } from '@app/drizzle/schema/expenses.schema';
import { AuditFieldsDto } from '../../shared/dto/audit-fields.dto';

export class AssetMaintenanceDto extends AuditFieldsDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Asset maintenance ID',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440001',
    description: 'Business ID',
  })
  businessId: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440002',
    description: 'Asset ID',
  })
  assetId: string;

  @ApiPropertyOptional({
    example: 'WO-2024-001',
    description: 'Work order number',
    nullable: true,
  })
  workOrderNumber?: string;

  @ApiProperty({
    example: 'Engine Oil Change',
    description: 'Maintenance title',
  })
  title: string;

  @ApiPropertyOptional({
    example: 'Regular engine oil change and filter replacement',
    description: 'Maintenance description',
    nullable: true,
  })
  description?: string;

  @ApiPropertyOptional({
    example: 'Use synthetic oil grade 5W-30',
    description: 'Maintenance instructions',
    nullable: true,
  })
  instructions?: string;

  @ApiProperty({
    example: MaintenanceStatus.PENDING,
    enum: MaintenanceStatus,
    enumName: 'MaintenanceStatus',
    description: 'Maintenance status',
  })
  status: MaintenanceStatus;

  @ApiProperty({
    example: MaintenancePriority.NORMAL,
    enum: MaintenancePriority,
    enumName: 'MaintenancePriority',
    description: 'Maintenance priority',
  })
  priority: MaintenancePriority;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440003',
    description: 'Supplier ID',
    nullable: true,
  })
  supplierId?: string;

  @ApiPropertyOptional({
    example: 'ABC Auto Services',
    description: 'Supplier name',
    nullable: true,
  })
  supplierName?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440004',
    description: 'Staff member ID assigned to maintenance',
    nullable: true,
  })
  assignedTo?: string;

  @ApiPropertyOptional({
    example: 'John Doe',
    description: 'Name of staff member assigned to maintenance',
    nullable: true,
  })
  assignedToName?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440005',
    description: 'Staff member ID for quality check',
    nullable: true,
  })
  qualityCheckBy?: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of staff member for quality check',
    nullable: true,
  })
  qualityCheckByName?: string;

  @ApiPropertyOptional({
    example: '2024-01-20T10:00:00Z',
    description: 'Target completion date',
    nullable: true,
  })
  targetCompletionDate?: string;

  @ApiPropertyOptional({
    example: '2024-01-15T08:30:00Z',
    description: 'Actual start date',
    nullable: true,
  })
  actualStartDate?: string;

  @ApiPropertyOptional({
    example: '2024-01-15T10:30:00Z',
    description: 'Completion timestamp',
    nullable: true,
  })
  completedAt?: string;

  @ApiPropertyOptional({
    example: 'Maintenance completed successfully',
    description: 'Completion notes',
    nullable: true,
  })
  completionNotes?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Expense account ID',
  })
  expenseAccountId: string;

  @ApiPropertyOptional({
    example: 'Maintenance Expenses',
    description: 'Expense account name',
    nullable: true,
  })
  expenseAccountName?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Payment account ID',
  })
  paymentAccountId: string;

  @ApiPropertyOptional({
    example: 'Cash Account',
    description: 'Payment account name',
    nullable: true,
  })
  paymentAccountName?: string;

  @ApiProperty({
    example: '2024-01-15',
    description: 'Payment date',
  })
  paymentDate: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Payment method ID',
  })
  paymentMethodId: string;

  @ApiPropertyOptional({
    example: 'Credit Card',
    description: 'Payment method name',
    nullable: true,
  })
  paymentMethodName?: string;

  @ApiPropertyOptional({
    example: 'REF-2024-001',
    description: 'Payment reference number',
    nullable: true,
  })
  paymentReferenceNumber?: string;

  @ApiProperty({
    example: AmountType.EXCLUSIVE_OF_TAX,
    enum: AmountType,
    enumName: 'AmountType',
    description: 'Amount type',
  })
  amountType: AmountType;

  @ApiProperty({
    example: '100.00',
    description: 'Subtotal amount',
  })
  subtotal: string;

  @ApiProperty({
    example: '115.00',
    description: 'Total amount',
  })
  total: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440009',
    description: 'Tax ID',
    nullable: true,
  })
  taxId?: string;

  @ApiPropertyOptional({
    example: 'VAT 15%',
    description: 'Tax name',
    nullable: true,
  })
  taxName?: string;

  @ApiPropertyOptional({
    example: 'Asset Name',
    description: 'Asset name',
    nullable: true,
  })
  assetName?: string;

  @ApiPropertyOptional({
    example: 'ASSET-001',
    description: 'Asset code',
    nullable: true,
  })
  assetCode?: string;

  @ApiPropertyOptional({
    example: 'John Creator',
    description: 'Name of user who created the maintenance',
    nullable: true,
  })
  createdByName?: string;

  @ApiPropertyOptional({
    example: 'Jane Updater',
    description: 'Name of user who last updated the maintenance',
    nullable: true,
  })
  updatedByName?: string;

  @ApiPropertyOptional({
    description: 'Array of attachment files',
    type: Array,
    example: [
      {
        id: '550e8400-e29b-41d4-a716-446655440012',
        originalName: 'maintenance_receipt.pdf',
        signedUrl: 'https://example.com/signed-url',
      },
    ],
  })
  attachments?: Array<{
    id: string;
    originalName: string;
    signedUrl: string;
  }>;
}
