import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  AppointmentStatus,
  BookingSource,
} from '../../drizzle/schema/appointments.schema';

export class AppointmentListDto {
  @ApiProperty({ description: 'Unique identifier for the appointment' })
  id: string;

  @ApiProperty({
    description: 'Unique appointment number',
    example: 'APT-001',
  })
  appointmentNumber: string;

  @ApiPropertyOptional({
    description: 'Customer name',
    example: '<PERSON>',
  })
  customerName?: string;

  @ApiPropertyOptional({
    description: 'Location name',
    example: 'Main Office',
  })
  locationName?: string;

  @ApiProperty({
    description: 'Appointment status',
    enum: AppointmentStatus,
    enumName: 'AppointmentStatus',
    example: AppointmentStatus.SCHEDULED,
  })
  status: AppointmentStatus;

  @ApiProperty({
    description: 'Appointment date and time',
    example: '2024-01-15T10:00:00Z',
  })
  appointmentDate: Date;

  @ApiPropertyOptional({
    description: 'Source of the booking',
    enum: BookingSource,
    enumName: 'BookingSource',
    example: BookingSource.ONLINE,
  })
  bookingSource?: BookingSource;

  @ApiPropertyOptional({
    description: 'Total estimated cost for the appointment',
    example: '150.00',
  })
  totalEstimatedCost?: string;

  @ApiPropertyOptional({
    description: 'Whether the deposit has been paid',
    example: false,
  })
  depositPaid?: boolean;

  @ApiProperty({
    description: 'Number of services associated with this appointment',
    example: 2,
  })
  servicesCount: number;

  @ApiProperty({
    description: 'Number of staff members assigned to this appointment',
    example: 1,
  })
  staffCount: number;
}
