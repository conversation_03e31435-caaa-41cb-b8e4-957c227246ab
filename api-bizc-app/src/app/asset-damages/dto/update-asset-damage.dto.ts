import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsUUID,
  IsBoolean,
  IsDateString,
  IsArray,
  IsNumber,
  Min,
  IsEnum,
  IsObject,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  DamageCategory,
  DamageSeverity,
} from '../../drizzle/schema/asset-damage.schema';
import { MediaCollectionDto } from '../../shared/dto/media-association.dto';

export class UpdateAssetDamageDto {
  @ApiPropertyOptional({
    description: 'Asset ID that has the damage',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  assetId?: string;

  @ApiPropertyOptional({
    description: 'Type of damage',
    example: 'Scratch',
  })
  @IsOptional()
  @IsString()
  damageType?: string;

  @ApiPropertyOptional({
    description: 'Damage category enum',
    enum: DamageCategory,
  })
  @IsOptional()
  @IsEnum(DamageCategory)
  damageCategory?: DamageCategory;

  @ApiPropertyOptional({
    description: 'Damage severity enum',
    enum: DamageSeverity,
  })
  @IsOptional()
  @IsEnum(DamageSeverity)
  severity?: DamageSeverity;

  @ApiPropertyOptional({
    description: 'Estimated cost of damage',
    example: '500.00',
  })
  @IsOptional()
  @IsString()
  amount?: string;

  @ApiPropertyOptional({
    description: 'Date and time when the incident occurred (ISO 8601)',
    example: '2024-01-15T14:30:00Z',
  })
  @IsOptional()
  @IsDateString()
  incidentAt?: string;

  @ApiPropertyOptional({
    description: 'Additional details about the damage',
    example: 'Damage occurred in parking lot',
  })
  @IsOptional()
  @IsString()
  details?: string;

  @ApiPropertyOptional({
    description: 'Whether the damage has been repaired',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isRepaired?: boolean;

  @ApiPropertyOptional({
    description: 'Timestamp when the damage was repaired',
    example: '2024-01-20T10:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  repairedAt?: string;

  @ApiPropertyOptional({
    description: 'Staff member ID who repaired the damage',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  repairedBy?: string;

  @ApiPropertyOptional({
    description: 'Repair method',
    example: 'repair_order',
  })
  @IsOptional()
  @IsString()
  repairMethod?: string;

  @ApiPropertyOptional({
    description: 'Repair order ID if applicable',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  repairOrderId?: string;

  @ApiPropertyOptional({
    description: 'Details/notes about the repair work done',
    example: 'Replaced bumper and repainted',
  })
  @IsOptional()
  @IsString()
  repairNotes?: string;

  @ApiPropertyOptional({
    description: 'Cost of the repair work',
    example: '450.00',
  })
  @IsOptional()
  @IsString()
  repairCost?: string;

  @ApiPropertyOptional({
    description: 'Expense account ID for the repair',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  repairExpenseAccountId?: string;

  @ApiPropertyOptional({
    description: 'Payment account ID used to pay for the repair',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  repairPaymentAccountId?: string;

  @ApiPropertyOptional({
    description: 'Flexible JSON object for asset-specific data',
    example: { vendor: 'ACME Repair Co.' },
  })
  @IsOptional()
  @IsObject()
  customFields?: Record<string, unknown>;

  @ApiPropertyOptional({
    description:
      'Array of attachment file indexes to use for this asset damage (0-based)',
    type: [Number],
    example: [0, 1, 2],
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Min(0, { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : [value];
      } catch {
        return [parseInt(value, 10)];
      }
    }
    if (Array.isArray(value)) {
      return value.map((v) => (typeof v === 'string' ? parseInt(v, 10) : v));
    }
    return value;
  })
  attachmentIndexes?: number[];

  @ApiPropertyOptional({
    description: 'Media association metadata for uploaded attachments',
    type: MediaCollectionDto,
  })
  @IsOptional()
  @Type(() => MediaCollectionDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return undefined;
      }
    }
    return value;
  })
  mediaMetadata?: MediaCollectionDto;
}
