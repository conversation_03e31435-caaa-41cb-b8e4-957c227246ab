import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  BedType,
  ViewType,
  RoomStatus,
  AccommodationUnitStatus,
  TaxType,
} from '../../shared/types';
import { AccommodationUnitFeatures } from '@shared/types';
import { AuditFieldsDto } from '../../shared/dto/audit-fields.dto';

export class AccommodationUnitDto extends AuditFieldsDto {
  @ApiProperty({ description: 'Accommodation unit ID' })
  id: string;

  @ApiProperty({ description: 'Business ID' })
  businessId: string;

  @ApiPropertyOptional({ description: 'Room number' })
  roomNumber?: string;

  @ApiPropertyOptional({ description: 'Custom name for the unit' })
  name?: string;

  @ApiProperty({ description: 'Type ID (references reservation-types)' })
  type: string;

  @ApiPropertyOptional({
    description: 'Sub-type ID (references reservation-types)',
  })
  subType?: string;

  @ApiProperty({
    enum: RoomStatus,
    enumName: 'RoomStatus',
    description: 'Current room status',
  })
  status: RoomStatus;

  @ApiProperty({ description: 'Floor number' })
  floor: number;

  @ApiPropertyOptional({ description: 'Building name/identifier' })
  building?: string;

  @ApiPropertyOptional({ description: 'Unit description' })
  description?: string;

  @ApiProperty({
    enum: BedType,
    enumName: 'BedType',
    description: 'Type of bed in the unit',
  })
  bedType: BedType;

  @ApiPropertyOptional({
    enum: ViewType,
    enumName: 'ViewType',
    description: 'View type from the unit',
  })
  viewType?: ViewType;

  @ApiPropertyOptional({ description: 'Unit features and amenities' })
  features?: AccommodationUnitFeatures;

  @ApiProperty({ description: 'Maximum number of adults' })
  maxAdults: number;

  @ApiProperty({ description: 'Maximum number of children' })
  maxChildren: number;

  @ApiProperty({ description: 'Base price per night' })
  basePrice: string;

  @ApiProperty({ description: 'Base weekend rate' })
  baseWeekendRate: string;

  @ApiProperty({ description: 'Base weekly rate' })
  baseWeeklyRate: string;

  @ApiProperty({ description: 'Base monthly rate' })
  baseMonthlyRate: string;

  @ApiProperty({ description: 'Standard cost' })
  standardCost: string;

  @ApiProperty({ description: 'Income account ID' })
  incomeAccountId: string;

  @ApiProperty({ description: 'Expense account ID' })
  expenseAccountId: string;

  @ApiPropertyOptional({ description: 'Asset account ID' })
  assetAccountId?: string;

  @ApiPropertyOptional({ description: 'Asset ID' })
  assetId?: string;

  @ApiPropertyOptional({ description: 'SEO title' })
  seoTitle?: string;

  @ApiPropertyOptional({ description: 'SEO description' })
  seoDescription?: string;

  @ApiPropertyOptional({ description: 'SEO keywords' })
  seoKeywords?: string[];

  @ApiPropertyOptional({ description: 'OG image ID' })
  ogImage?: string;

  @ApiProperty({ description: 'Type position for ordering' })
  typePosition: number;

  @ApiProperty({ description: 'Sub-type position for ordering' })
  subTypePosition: number;

  @ApiProperty({ description: 'Global position for ordering' })
  globalPosition: number;

  @ApiProperty({
    enum: TaxType,
    enumName: 'TaxType',
    description: 'Tax type',
  })
  taxType: TaxType;

  @ApiPropertyOptional({ description: 'Default tax rate ID' })
  defaultTaxRateId?: string;

  @ApiProperty({
    enum: AccommodationUnitStatus,
    enumName: 'AccommodationUnitStatus',
    description: 'System status',
  })
  systemStatus: AccommodationUnitStatus;

  // Related entities
  @ApiPropertyOptional({ description: 'Type information' })
  typeInfo?: {
    id: string;
    name: string;
    category?: string;
  };

  @ApiPropertyOptional({ description: 'Sub-type information' })
  subTypeInfo?: {
    id: string;
    name: string;
    category?: string;
  };

  @ApiPropertyOptional({ description: 'Income account information' })
  incomeAccount?: {
    id: string;
    name: string;
    code?: string;
  };

  @ApiPropertyOptional({ description: 'Expense account information' })
  expenseAccount?: {
    id: string;
    name: string;
    code?: string;
  };

  @ApiPropertyOptional({ description: 'Asset account information' })
  assetAccount?: {
    id: string;
    name: string;
    code?: string;
  };

  @ApiPropertyOptional({ description: 'Asset information' })
  asset?: {
    id: string;
    name: string;
    code?: string;
  };

  @ApiPropertyOptional({ description: 'Default tax rate information' })
  defaultTaxRate?: {
    id: string;
    name: string;
    rate: string;
  };

  @ApiPropertyOptional({ description: 'Image URLs' })
  images?: string[];

  @ApiPropertyOptional({ description: 'OG image URL' })
  ogImageUrl?: string;
}
