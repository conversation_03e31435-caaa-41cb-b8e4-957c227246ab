import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAssetDamageDto } from './dto/create-asset-damage.dto';
import { UpdateAssetDamageDto } from './dto/update-asset-damage.dto';
import { AssetDamageDto } from './dto/asset-damage.dto';
import { AssetDamageListDto } from './dto/asset-damage-list.dto';
import { AssetDamageIdResponseDto } from './dto/asset-damage-id-response.dto';
import { BulkDeleteAssetDamageResponseDto } from './dto/bulk-delete-asset-damage-response.dto';
import { PaginatedAssetDamagesResponseDto } from './dto/paginated-asset-damages-response.dto';
import { assetDamage } from '../drizzle/schema/asset-damage.schema';
import { assets } from '../drizzle/schema/assets.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { users } from '../drizzle/schema/users.schema';

import {
  and,
  eq,
  desc,
  asc,
  gte,
  lte,
  ilike,
  inArray,
  count,
  sql,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType } from '../shared/types';
import { MediaService } from '../media/media.service';
import { MediaAssociationService } from '../shared/services/media-association.service';
import { MediaReferenceType } from '../drizzle/schema/media.schema';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { AuditFieldsService } from '../shared/services/audit-fields.service';

@Injectable()
export class AssetDamagesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
    private mediaService: MediaService,
    private readonly mediaAssociationService: MediaAssociationService,
    private readonly auditFieldsService: AuditFieldsService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAssetDamageDto: CreateAssetDamageDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate asset exists and belongs to the business
      const assetExists = await this.db
        .select({ id: assets.id })
        .from(assets)
        .where(
          and(
            eq(assets.id, createAssetDamageDto.assetId),
            eq(assets.businessId, businessId),
            eq(assets.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!assetExists) {
        throw new BadRequestException(
          'Asset not found or does not belong to this business',
        );
      }

      // Validate repairedBy staff member if provided
      if (createAssetDamageDto.repairedBy) {
        const staffExists = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, createAssetDamageDto.repairedBy),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffExists) {
          throw new BadRequestException(
            'Repaired by staff member not found or does not belong to this business',
          );
        }
      }

      const result = await this.db
        .insert(assetDamage)
        .values({
          businessId,
          assetId: createAssetDamageDto.assetId,
          damageType: createAssetDamageDto.damageType,
          damageCategory: createAssetDamageDto.damageCategory,
          severity: createAssetDamageDto.severity,
          amount: createAssetDamageDto.amount,
          incidentAt: new Date(createAssetDamageDto.incidentAt),
          details: createAssetDamageDto.details,
          isRepaired: createAssetDamageDto.isRepaired || false,
          repairedAt: createAssetDamageDto.repairedAt
            ? new Date(createAssetDamageDto.repairedAt)
            : undefined,
          repairedBy: createAssetDamageDto.repairedBy,
          repairMethod: createAssetDamageDto.repairMethod,
          repairOrderId: createAssetDamageDto.repairOrderId,
          repairNotes: createAssetDamageDto.repairNotes,
          repairCost: createAssetDamageDto.repairCost,
          repairExpenseAccountId: createAssetDamageDto.repairExpenseAccountId,
          repairPaymentAccountId: createAssetDamageDto.repairPaymentAccountId,
          customFields: createAssetDamageDto.customFields,
          createdBy: userId,
        })
        .returning({ id: assetDamage.id });

      const assetDamageId = result[0].id;

      // Log activity
      this.activityLogService
        .logCreate(assetDamageId, EntityType.ASSET_DAMAGE, userId, businessId, {
          reason: 'Asset damage record created',
        })
        .catch((error) => {
          console.warn(
            'Failed to log asset damage creation activity:',
            error.message,
          );
        });

      return { id: assetDamageId };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create asset damage record');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAssetDamageDto: CreateAssetDamageDto,
    attachmentFiles?: Express.Multer.File[],
    // metadata is currently unused; kept for API signature compatibility
    _metadata?: ActivityMetadata,
  ): Promise<AssetDamageIdResponseDto> {
    // Mark unused parameter as intentionally unused for linters
    void _metadata;
    const result = await this.create(userId, businessId, createAssetDamageDto);

    // Handle file attachments if provided (attachments only, no images)
    if (attachmentFiles && attachmentFiles.length > 0) {
      const filesToUpload = createAssetDamageDto.attachmentIndexes
        ? createAssetDamageDto.attachmentIndexes
            .map((index: number) => attachmentFiles[index])
            .filter(Boolean)
        : attachmentFiles;

      if (filesToUpload.length > 0) {
        await this.mediaAssociationService.handleMediaAssociations(
          result.id,
          businessId,
          userId,
          MediaReferenceType.ASSET_DAMAGES,
          undefined, // images: none for asset damages
          filesToUpload, // attachments only
          createAssetDamageDto.mediaMetadata ?? {
            attachmentDefaults: { purpose: 'attachment' as any },
          },
        );
      }
    }

    return {
      id: result.id,
      message: 'Asset damage record created successfully',
    };
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    assetId?: string,
    damageType?: string,
    isRepaired?: string,
    sort?: string,
  ): Promise<PaginatedAssetDamagesResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(assetDamage.isDeleted, false),
      eq(assetDamage.businessId, businessId),
    ];

    // Add filters
    if (from) {
      whereConditions.push(gte(assetDamage.incidentAt, new Date(from)));
    }
    if (to) {
      whereConditions.push(lte(assetDamage.incidentAt, new Date(to)));
    }
    if (assetId) {
      whereConditions.push(eq(assetDamage.assetId, assetId));
    }
    if (damageType) {
      whereConditions.push(ilike(assetDamage.damageType, `%${damageType}%`));
    }
    if (isRepaired !== undefined) {
      whereConditions.push(eq(assetDamage.isRepaired, isRepaired === 'true'));
    }

    // Build sort order
    let orderBy: any;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'incidentAt':
          orderBy = isDesc
            ? desc(assetDamage.incidentAt)
            : asc(assetDamage.incidentAt);
          break;
        case 'amount':
          orderBy = isDesc ? desc(assetDamage.amount) : asc(assetDamage.amount);
          break;
        case 'damageType':
          orderBy = isDesc
            ? desc(assetDamage.damageType)
            : asc(assetDamage.damageType);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(assetDamage.createdAt)
            : asc(assetDamage.createdAt);
          break;
        default:
          orderBy = desc(assetDamage.createdAt);
      }
    } else {
      orderBy = desc(assetDamage.createdAt);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(assetDamage)
      .where(and(...whereConditions));

    const total = totalResult[0].count;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data
    const results = await this.db
      .select({
        id: assetDamage.id,
        assetId: assetDamage.assetId,
        assetName: sql<string>`CASE 
          WHEN ${assets.name} IS NOT NULL
          THEN ${assets.name}
          ELSE 'Unknown Asset'
        END`,
        damageType: assetDamage.damageType,
        damageCategory: assetDamage.damageCategory,
        severity: assetDamage.severity,
        amount: assetDamage.amount,
        incidentAt: assetDamage.incidentAt,
        isRepaired: assetDamage.isRepaired,
        repairedAt: assetDamage.repairedAt,
        repairCost: assetDamage.repairCost,
        createdAt: assetDamage.createdAt,
        updatedAt: assetDamage.updatedAt,
      })
      .from(assetDamage)
      .leftJoin(assets, eq(assetDamage.assetId, assets.id))
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    const data: AssetDamageListDto[] = results.map((row) => ({
      id: row.id,
      assetId: row.assetId,
      assetName: row.assetName || 'Unknown Asset',
      damageType: row.damageType,
      damageCategory: row.damageCategory,
      severity: row.severity,
      amount: row.amount,
      incidentAt: row.incidentAt?.toISOString?.() ?? String(row.incidentAt),
      isRepaired: row.isRepaired,
      repairedAt: row.repairedAt?.toISOString?.(),
      repairCost: row.repairCost,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<AssetDamageDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: assetDamage.id,
        businessId: assetDamage.businessId,
        assetId: assetDamage.assetId,
        assetName: sql<string>`CASE
          WHEN ${assets.name} IS NOT NULL
          THEN ${assets.name}
          ELSE 'Unknown Asset'
        END`,
        damageType: assetDamage.damageType,
        damageCategory: assetDamage.damageCategory,
        severity: assetDamage.severity,
        amount: assetDamage.amount,
        incidentAt: assetDamage.incidentAt,
        details: assetDamage.details,
        isRepaired: assetDamage.isRepaired,
        repairedAt: assetDamage.repairedAt,
        repairedBy: assetDamage.repairedBy,
        repairedByName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        repairMethod: assetDamage.repairMethod,
        repairOrderId: assetDamage.repairOrderId,
        repairNotes: assetDamage.repairNotes,
        repairCost: assetDamage.repairCost,
        repairExpenseAccountId: assetDamage.repairExpenseAccountId,
        repairPaymentAccountId: assetDamage.repairPaymentAccountId,
        customFields: assetDamage.customFields,
        createdBy: assetDamage.createdBy,
        createdByName: users.displayName,
        updatedBy: assetDamage.updatedBy,
        createdAt: assetDamage.createdAt,
        updatedAt: assetDamage.updatedAt,
      })
      .from(assetDamage)
      .leftJoin(assets, eq(assetDamage.assetId, assets.id))
      .leftJoin(staffMembers, eq(assetDamage.repairedBy, staffMembers.id))
      .leftJoin(users, eq(assetDamage.createdBy, users.id))
      .where(
        and(
          eq(assetDamage.id, id),
          eq(assetDamage.businessId, businessId),
          eq(assetDamage.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Asset damage record not found');
    }

    // Get attachments
    const attachments = await this.getAttachments(result.id, businessId);

    const baseDto = {
      id: result.id,
      businessId: result.businessId,
      assetId: result.assetId,
      assetName: result.assetName || 'Unknown Asset',
      damageType: result.damageType,
      damageCategory: result.damageCategory,
      severity: result.severity,
      amount: result.amount,
      incidentAt:
        result.incidentAt?.toISOString?.() ?? String(result.incidentAt),
      details: result.details,
      isRepaired: result.isRepaired,
      repairedAt: result.repairedAt?.toISOString?.(),
      repairedBy: result.repairedBy,
      repairedByName: result.repairedByName,
      repairMethod: result.repairMethod,
      repairOrderId: result.repairOrderId,
      repairNotes: result.repairNotes,
      repairCost: result.repairCost,
      repairExpenseAccountId: result.repairExpenseAccountId,
      repairPaymentAccountId: result.repairPaymentAccountId,
      customFields: (result.customFields ?? {}) as Record<string, unknown>,
      attachments,
    };

    return this.auditFieldsService.populateAuditFields(result, baseDto);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetDamageDto: UpdateAssetDamageDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if asset damage record exists
      const existingRecord = await this.db
        .select({ id: assetDamage.id })
        .from(assetDamage)
        .where(
          and(
            eq(assetDamage.id, id),
            eq(assetDamage.businessId, businessId),
            eq(assetDamage.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingRecord) {
        throw new NotFoundException('Asset damage record not found');
      }

      // Validate asset if provided
      if (updateAssetDamageDto.assetId) {
        const assetExists = await this.db
          .select({ id: assets.id })
          .from(assets)
          .where(
            and(
              eq(assets.id, updateAssetDamageDto.assetId),
              eq(assets.businessId, businessId),
              eq(assets.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!assetExists) {
          throw new BadRequestException(
            'Asset not found or does not belong to this business',
          );
        }
      }

      // Validate repairedBy staff member if provided
      if (updateAssetDamageDto.repairedBy) {
        const staffExists = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updateAssetDamageDto.repairedBy),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffExists) {
          throw new BadRequestException(
            'Repaired by staff member not found or does not belong to this business',
          );
        }
      }

      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Only update provided fields
      if (updateAssetDamageDto.assetId !== undefined) {
        updateData.assetId = updateAssetDamageDto.assetId;
      }
      if (updateAssetDamageDto.damageType !== undefined) {
        updateData.damageType = updateAssetDamageDto.damageType;
      }
      if (updateAssetDamageDto.damageCategory !== undefined) {
        updateData.damageCategory = updateAssetDamageDto.damageCategory;
      }
      if (updateAssetDamageDto.severity !== undefined) {
        updateData.severity = updateAssetDamageDto.severity;
      }
      if (updateAssetDamageDto.amount !== undefined) {
        updateData.amount = updateAssetDamageDto.amount;
      }
      if (updateAssetDamageDto.incidentAt !== undefined) {
        updateData.incidentAt = updateAssetDamageDto.incidentAt;
      }
      if (updateAssetDamageDto.details !== undefined) {
        updateData.details = updateAssetDamageDto.details;
      }
      if (updateAssetDamageDto.isRepaired !== undefined) {
        updateData.isRepaired = updateAssetDamageDto.isRepaired;
      }
      if (updateAssetDamageDto.repairedAt !== undefined) {
        updateData.repairedAt = updateAssetDamageDto.repairedAt;
      }
      if (updateAssetDamageDto.repairedBy !== undefined) {
        updateData.repairedBy = updateAssetDamageDto.repairedBy;
      }
      if (updateAssetDamageDto.repairMethod !== undefined) {
        updateData.repairMethod = updateAssetDamageDto.repairMethod;
      }
      if (updateAssetDamageDto.repairOrderId !== undefined) {
        updateData.repairOrderId = updateAssetDamageDto.repairOrderId;
      }
      if (updateAssetDamageDto.repairNotes !== undefined) {
        updateData.repairNotes = updateAssetDamageDto.repairNotes;
      }
      if (updateAssetDamageDto.repairCost !== undefined) {
        updateData.repairCost = updateAssetDamageDto.repairCost;
      }
      if (updateAssetDamageDto.repairExpenseAccountId !== undefined) {
        updateData.repairExpenseAccountId =
          updateAssetDamageDto.repairExpenseAccountId;
      }
      if (updateAssetDamageDto.repairPaymentAccountId !== undefined) {
        updateData.repairPaymentAccountId =
          updateAssetDamageDto.repairPaymentAccountId;
      }
      if (updateAssetDamageDto.customFields !== undefined) {
        updateData.customFields = updateAssetDamageDto.customFields;
      }

      await this.db
        .update(assetDamage)
        .set(updateData)
        .where(eq(assetDamage.id, id));

      // Log activity
      this.activityLogService
        .logUpdate(id, EntityType.ASSET_DAMAGE, userId, businessId, {
          source: metadata?.source,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        })
        .catch((error) => {
          console.warn(
            'Failed to log asset damage update activity:',
            error.message,
          );
        });

      return { id };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update asset damage record');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetDamageDto: UpdateAssetDamageDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetDamageIdResponseDto> {
    const result = await this.update(
      userId,
      businessId,
      id,
      updateAssetDamageDto,
      metadata,
    );

    // Handle file attachments if provided (attachments only, replace all)
    if (attachmentFiles && attachmentFiles.length > 0) {
      const filesToUpload = updateAssetDamageDto.attachmentIndexes
        ? updateAssetDamageDto.attachmentIndexes
            .map((index: number) => attachmentFiles[index])
            .filter(Boolean)
        : attachmentFiles;

      if (filesToUpload.length > 0) {
        // Replace existing attachments: delete associations and re-upload
        await this.mediaService.deleteMediaByReferenceId(
          id,
          businessId,
          `${businessId}/${MediaReferenceType.ASSET_DAMAGES}`,
        );
        await this.mediaAssociationService.handleMediaAssociations(
          id,
          businessId,
          userId,
          MediaReferenceType.ASSET_DAMAGES,
          undefined,
          filesToUpload,
          updateAssetDamageDto.mediaMetadata ?? {
            attachmentDefaults: { purpose: 'attachment' as any },
          },
        );
      }
    }

    return {
      id: result.id,
      message: 'Asset damage record updated successfully',
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if asset damage record exists
      const existingRecord = await this.db
        .select({ id: assetDamage.id })
        .from(assetDamage)
        .where(
          and(
            eq(assetDamage.id, id),
            eq(assetDamage.businessId, businessId),
            eq(assetDamage.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingRecord) {
        throw new NotFoundException('Asset damage record not found');
      }

      // Soft delete
      await this.db
        .update(assetDamage)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(assetDamage.id, id));

      // Log activity
      this.activityLogService
        .logDelete(id, EntityType.ASSET_DAMAGE, userId, businessId, {
          source: metadata?.source,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        })
        .catch((error) => {
          console.warn(
            'Failed to log asset damage deletion activity:',
            error.message,
          );
        });

      return { id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to delete asset damage record');
    }
  }

  async removeAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<AssetDamageIdResponseDto> {
    const result = await this.remove(userId, businessId, id, metadata);
    return {
      id: result.id,
      message: 'Asset damage record deleted successfully',
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<BulkDeleteAssetDamageResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!ids || ids.length === 0) {
        throw new BadRequestException('No IDs provided for deletion');
      }

      // Check which records exist
      const existingRecords = await this.db
        .select({ id: assetDamage.id })
        .from(assetDamage)
        .where(
          and(
            inArray(assetDamage.id, ids),
            eq(assetDamage.businessId, businessId),
            eq(assetDamage.isDeleted, false),
          ),
        );

      const existingIds = existingRecords.map((record) => record.id);
      const notFoundIds = ids.filter((id) => !existingIds.includes(id));

      if (existingIds.length === 0) {
        throw new NotFoundException('No asset damage records found to delete');
      }

      // Soft delete existing records
      await this.db
        .update(assetDamage)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(assetDamage.id, existingIds));

      // Log activity for each deleted record
      for (const recordId of existingIds) {
        this.activityLogService
          .logDelete(recordId, EntityType.ASSET_DAMAGE, userId, businessId, {
            source: metadata?.source,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          })
          .catch((error) => {
            console.warn(
              `Failed to log asset damage deletion activity for record ${recordId}:`,
              error.message,
            );
          });
      }

      return {
        deletedIds: existingIds,
        notFoundIds,
        message: `Successfully deleted ${existingIds.length} asset damage record(s)`,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to bulk delete asset damage records',
      );
    }
  }

  private async getAttachments(
    assetDamageId: string,
    businessId: string,
  ): Promise<Array<{ id: string; originalName: string; signedUrl: string }>> {
    try {
      const associations = await this.mediaAssociationService.getAttachments(
        assetDamageId,
        businessId,
        MediaReferenceType.ASSET_DAMAGES,
      );

      return associations.map((a) => ({
        id: a.mediaId,
        originalName: a.originalName,
        signedUrl: a.publicUrl,
      }));
    } catch (error) {
      console.warn(
        `Failed to get attachments for asset damage ${assetDamageId}:`,
        error.message,
      );
      return [];
    }
  }
}
