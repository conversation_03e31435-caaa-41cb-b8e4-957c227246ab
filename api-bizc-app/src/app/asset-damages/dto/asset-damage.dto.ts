import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AuditFieldsDto } from '../../shared/dto/audit-fields.dto';
import {
  DamageCategory,
  DamageSeverity,
} from '../../drizzle/schema/asset-damage.schema';

export class AssetDamageDto extends AuditFieldsDto {
  @ApiProperty({
    description: 'Unique identifier for the asset damage record',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Business ID the asset damage belongs to',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  businessId: string;

  @ApiProperty({
    description: 'Asset ID that has the damage',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  assetId: string;

  @ApiPropertyOptional({
    description: 'Asset display name',
    example: 'Toyota Camry 2020',
  })
  assetName?: string;

  @ApiProperty({
    description: 'Type of damage',
    example: 'Scratch',
  })
  damageType: string;

  @ApiProperty({
    description: 'Damage category enum',
    enum: DamageCategory,
    example: DamageCategory.PHYSICAL,
  })
  damageCategory: DamageCategory;

  @ApiProperty({
    description: 'Damage severity enum',
    enum: DamageSeverity,
    example: DamageSeverity.MINOR,
  })
  severity: DamageSeverity;

  @ApiProperty({
    description: 'Estimated cost of damage',
    example: '500.00',
  })
  amount: string;

  @ApiProperty({
    description: 'Date and time when the incident occurred',
    example: '2024-01-15T14:30:00Z',
  })
  incidentAt: string;

  @ApiPropertyOptional({
    description: 'Additional details about the damage',
    example: 'Damage occurred in parking lot',
  })
  details?: string;

  @ApiProperty({
    description: 'Whether the damage has been repaired',
    example: false,
  })
  isRepaired: boolean;

  @ApiPropertyOptional({
    description: 'Timestamp when the damage was repaired',
    example: '2024-01-20T10:00:00Z',
  })
  repairedAt?: string;

  @ApiPropertyOptional({
    description: 'Staff member ID who repaired the damage',
    example: '550e8400-e29b-41d4-a716-446655440003',
  })
  repairedBy?: string;

  @ApiPropertyOptional({
    description: 'Name of staff member who repaired the damage',
    example: 'John Smith',
  })
  repairedByName?: string;

  @ApiPropertyOptional({
    description: 'Repair method',
    example: 'repair_order',
  })
  repairMethod?: string;

  @ApiPropertyOptional({
    description: 'Repair order ID if applicable',
    example: '550e8400-e29b-41d4-a716-************',
  })
  repairOrderId?: string;

  @ApiPropertyOptional({
    description: 'Details/notes about the repair work done',
    example: 'Replaced bumper and repainted',
  })
  repairNotes?: string;

  @ApiPropertyOptional({
    description: 'Cost of the repair work',
    example: '450.00',
  })
  repairCost?: string;

  @ApiPropertyOptional({
    description: 'Expense account ID for the repair',
    example: '550e8400-e29b-41d4-a716-************',
  })
  repairExpenseAccountId?: string;

  @ApiPropertyOptional({
    description: 'Payment account ID used to pay for the repair',
    example: '550e8400-e29b-41d4-a716-************',
  })
  repairPaymentAccountId?: string;

  @ApiPropertyOptional({
    description: 'Flexible JSON object for asset-specific data',
    example: { vendor: 'ACME Repair Co.' },
  })
  customFields?: Record<string, unknown>;

  @ApiPropertyOptional({
    description: 'Name of user who created the record',
    example: 'Admin User',
  })
  createdByName?: string;

  @ApiPropertyOptional({
    description: 'Name of user who last updated the record',
    example: 'Manager User',
  })
  updatedByName?: string;

  @ApiPropertyOptional({
    description: 'Array of attachment files',
    type: Array,
    example: [
      {
        id: '550e8400-e29b-41d4-a716-************',
        originalName: 'damage_photo.jpg',
        signedUrl: 'https://example.com/signed-url',
      },
    ],
  })
  attachments?: Array<{
    id: string;
    originalName: string;
    signedUrl: string;
  }>;
}
