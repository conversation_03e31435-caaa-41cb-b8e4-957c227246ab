import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  AccommodationReservationStatus,
  ReservationSource,
} from '../../shared/types/accommodation.enum';
import { PaymentStatus } from '../../shared/types/common.enum';

export class AccommodationReservationListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Accommodation reservation ID',
  })
  id: string;

  @ApiProperty({
    example: 'RES-2024-001',
    description: 'Reservation number',
  })
  reservationNumber: string;

  @ApiPropertyOptional({
    example: 'EXT-REF-123',
    description: 'External reference number',
  })
  referenceNumber?: string;

  @ApiProperty({
    example: '2024-01-15T15:00:00Z',
    description: 'Check-in date',
  })
  checkInDate: string;

  @ApiProperty({
    example: '2024-01-17T11:00:00Z',
    description: 'Check-out date',
  })
  checkOutDate: string;

  @ApiProperty({
    example: AccommodationReservationStatus.CONFIRMED,
    enum: AccommodationReservationStatus,
    enumName: 'AccommodationReservationStatus',
    description: 'Reservation status',
  })
  status: AccommodationReservationStatus;

  @ApiPropertyOptional({
    example: ReservationSource.ONLINE,
    enum: ReservationSource,
    enumName: 'ReservationSource',
    description: 'Reservation source',
  })
  reservationSource?: ReservationSource;

  @ApiProperty({
    example: PaymentStatus.PARTIAL,
    enum: PaymentStatus,
    enumName: 'PaymentStatus',
    description: 'Payment status',
  })
  paymentStatus: PaymentStatus;

  @ApiProperty({
    example: '300.00',
    description: 'Subtotal amount',
  })
  subtotal: string;

  @ApiProperty({
    example: '345.00',
    description: 'Total amount',
  })
  total: string;

  @ApiPropertyOptional({
    example: '100.00',
    description: 'Deposit paid amount',
  })
  depositPaid?: string;

  @ApiPropertyOptional({
    example: '245.00',
    description: 'Balance due amount',
  })
  balanceDue?: string;

  @ApiProperty({
    example: 2,
    description: 'Number of accommodation units',
  })
  unitsCount: number;

  @ApiProperty({
    example: 4,
    description: 'Total number of guests',
  })
  guestsCount: number;

  @ApiProperty({
    example: 'John Doe',
    description: 'Primary guest name',
  })
  primaryGuestName: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Primary guest email',
  })
  primaryGuestEmail: string;

  @ApiPropertyOptional({
    example: '+1234567890',
    description: 'Primary guest phone number',
  })
  primaryGuestPhone?: string;
}
