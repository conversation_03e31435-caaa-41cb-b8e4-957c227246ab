import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RepairStatus, RepairPriority, RepairType } from '../../shared/types';

export class AssetRepairOrderListDto {
  @ApiProperty({ description: 'Unique identifier for the asset repair order' })
  id: string;

  @ApiProperty({ description: 'Repair order number' })
  repairOrderNumber: string;

  @ApiProperty({ description: 'Title of the repair order' })
  title: string;

  @ApiProperty({ description: 'Description of the problem' })
  problemDescription: string;

  @ApiProperty({
    description: 'Type of repair',
    enum: RepairType,
    enumName: 'RepairType',
    example: RepairType.CORRECTIVE,
  })
  repairType: RepairType;

  @ApiProperty({
    description: 'Current status of the repair order',
    enum: RepairStatus,
    enumName: 'RepairStatus',
    example: RepairStatus.REPORTED,
  })
  status: RepairStatus;

  @ApiProperty({
    description: 'Priority level of the repair',
    enum: RepairPriority,
    enumName: 'RepairPriority',
    example: RepairPriority.NORMAL,
  })
  priority: RepairPriority;

  @ApiProperty({
    description: 'When the issue was reported',
    example: '2023-01-01T00:00:00Z',
  })
  reportedAt: Date;

  @ApiPropertyOptional({
    description: 'Target completion date',
    example: '2023-01-15T00:00:00Z',
    nullable: true,
  })
  targetCompletionDate?: Date;

  @ApiPropertyOptional({
    description: 'Actual completion date',
    example: '2023-01-10T00:00:00Z',
    nullable: true,
  })
  completedAt?: Date;

  @ApiProperty({ description: 'Asset name' })
  assetName: string;

  @ApiProperty({ description: 'Asset code' })
  assetCode: string;

  @ApiProperty({ description: 'Name of staff member who reported the issue' })
  reportedByName: string;

  @ApiPropertyOptional({
    description: 'Name of staff member assigned to the repair',
    nullable: true,
  })
  assignedToName?: string;

  @ApiPropertyOptional({
    description: 'Supplier name for external repairs',
    nullable: true,
  })
  supplierName?: string;

  @ApiProperty({
    description: 'Whether this is a warranty repair',
    example: false,
  })
  isWarrantyRepair: boolean;

  @ApiProperty({ description: 'Expense account name' })
  expenseAccountName: string;

  @ApiProperty({
    description: 'Total amount',
    example: '1100.00',
  })
  total: string;
}
