CREATE TYPE "public"."asset_adjustment_type" AS ENUM('depreciation', 'revaluation', 'impairment', 'correction');--> statement-breakpoint
ALTER TYPE "public"."media_reference_type" ADD VALUE 'asset-damages' BEFORE 'staff';--> statement-breakpoint
ALTER TYPE "public"."media_reference_type" ADD VALUE 'asset-repair-orders' BEFORE 'staff';--> statement-breakpoint
CREATE TABLE "asset_adjustments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"created_by" uuid NOT NULL,
	"updated_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"business_id" uuid NOT NULL,
	"asset_id" uuid NOT NULL,
	"adjustment_type" "asset_adjustment_type" NOT NULL,
	"adjustment_date" date NOT NULL,
	"effective_period" varchar(7),
	"amount" numeric(15, 2) NOT NULL,
	"book_value_before" numeric(15, 2) NOT NULL,
	"book_value_after" numeric(15, 2) NOT NULL,
	"description" text,
	"reference_no" varchar(100),
	"status" varchar(20) DEFAULT 'posted' NOT NULL,
	"journal_entry_id" uuid
);
--> statement-breakpoint
ALTER TABLE "asset_damage" DROP CONSTRAINT "asset_damage_responsible_party_id_staff_members_id_fk";
--> statement-breakpoint
ALTER TABLE "asset_damage" DROP CONSTRAINT "asset_damage_repair_vendor_id_suppliers_id_fk";
--> statement-breakpoint
ALTER TABLE "asset_damage" DROP CONSTRAINT "asset_damage_fixed_by_staff_members_id_fk";
--> statement-breakpoint
ALTER TABLE "asset_damage" DROP CONSTRAINT "asset_damage_checked_by_staff_members_id_fk";
--> statement-breakpoint
ALTER TABLE "asset_damage" DROP CONSTRAINT "asset_damage_approved_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "assets" DROP CONSTRAINT "assets_category_id_asset_categories_id_fk";
--> statement-breakpoint
ALTER TABLE "assets" DROP CONSTRAINT "assets_sub_category_id_asset_categories_id_fk";
--> statement-breakpoint
DROP INDEX "asset_damage_incident_date_index";--> statement-breakpoint
DROP INDEX "asset_damage_is_fixed_index";--> statement-breakpoint
DROP INDEX "asset_damage_fixed_by_index";--> statement-breakpoint
DROP INDEX "asset_damage_checked_by_index";--> statement-breakpoint
DROP INDEX "asset_damage_repair_status_index";--> statement-breakpoint
DROP INDEX "asset_damage_priority_index";--> statement-breakpoint
DROP INDEX "asset_damage_responsible_party_index";--> statement-breakpoint
DROP INDEX "asset_damage_repair_vendor_index";--> statement-breakpoint
DROP INDEX "asset_damage_approved_by_index";--> statement-breakpoint
DROP INDEX "asset_damage_asset_status_index";--> statement-breakpoint
DROP INDEX "asset_damage_business_date_index";--> statement-breakpoint
DROP INDEX "asset_damage_severity_priority_index";--> statement-breakpoint
DROP INDEX "asset_damage_repair_status_business_index";--> statement-breakpoint
DROP INDEX "asset_damage_business_priority_index";--> statement-breakpoint
DROP INDEX "assets_category_id_index";--> statement-breakpoint
DROP INDEX "assets_sub_category_id_index";--> statement-breakpoint
DROP INDEX "assets_type_index";--> statement-breakpoint
DROP INDEX "assets_business_category_index";--> statement-breakpoint
DROP INDEX "assets_business_type_index";--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "incident_at" timestamp NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_order_id" uuid;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "is_repaired" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_method" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repaired_at" timestamp;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repaired_by" uuid;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_notes" text;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_cost" numeric(12, 2);--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_expense_account_id" uuid;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "repair_payment_account_id" uuid;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD COLUMN "custom_fields" jsonb;--> statement-breakpoint
ALTER TABLE "asset_maintenances" ADD COLUMN "custom_fields" jsonb;--> statement-breakpoint
ALTER TABLE "asset_repair_orders" ADD COLUMN "custom_fields" jsonb;--> statement-breakpoint
ALTER TABLE "asset_repair_orders" ADD COLUMN "metadata" jsonb;--> statement-breakpoint
ALTER TABLE "assets" ADD COLUMN "custom_fields" jsonb;--> statement-breakpoint
ALTER TABLE "assets" ADD COLUMN "asset_type_id" uuid;--> statement-breakpoint
ALTER TABLE "assets" ADD COLUMN "asset_sub_type_id" uuid;--> statement-breakpoint
ALTER TABLE "asset_adjustments" ADD CONSTRAINT "asset_adjustments_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_adjustments" ADD CONSTRAINT "asset_adjustments_updated_by_users_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_adjustments" ADD CONSTRAINT "asset_adjustments_business_id_business_id_fk" FOREIGN KEY ("business_id") REFERENCES "public"."business"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_adjustments" ADD CONSTRAINT "asset_adjustments_asset_id_assets_id_fk" FOREIGN KEY ("asset_id") REFERENCES "public"."assets"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_adjustments" ADD CONSTRAINT "asset_adjustments_journal_entry_id_journal_entries_id_fk" FOREIGN KEY ("journal_entry_id") REFERENCES "public"."journal_entries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "asset_adjustments_id_index" ON "asset_adjustments" USING btree ("id");--> statement-breakpoint
CREATE INDEX "asset_adjustments_business_id_index" ON "asset_adjustments" USING btree ("business_id");--> statement-breakpoint
CREATE INDEX "asset_adjustments_created_by_index" ON "asset_adjustments" USING btree ("created_by");--> statement-breakpoint
CREATE INDEX "asset_adjustments_updated_by_index" ON "asset_adjustments" USING btree ("updated_by");--> statement-breakpoint
CREATE INDEX "asset_adjustments_created_at_index" ON "asset_adjustments" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "asset_adjustments_updated_at_index" ON "asset_adjustments" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX "asset_adjustments_is_deleted_index" ON "asset_adjustments" USING btree ("is_deleted");--> statement-breakpoint
CREATE INDEX "asset_adjustments_asset_idx" ON "asset_adjustments" USING btree ("asset_id");--> statement-breakpoint
CREATE INDEX "asset_adjustments_date_idx" ON "asset_adjustments" USING btree ("adjustment_date");--> statement-breakpoint
CREATE INDEX "asset_adjustments_type_idx" ON "asset_adjustments" USING btree ("adjustment_type");--> statement-breakpoint
CREATE INDEX "asset_adjustments_journal_entry_id_index" ON "asset_adjustments" USING btree ("journal_entry_id");--> statement-breakpoint
ALTER TABLE "asset_damage" ADD CONSTRAINT "asset_damage_repair_order_id_asset_repair_orders_id_fk" FOREIGN KEY ("repair_order_id") REFERENCES "public"."asset_repair_orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD CONSTRAINT "asset_damage_repaired_by_staff_members_id_fk" FOREIGN KEY ("repaired_by") REFERENCES "public"."staff_members"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD CONSTRAINT "asset_damage_repair_expense_account_id_accounts_id_fk" FOREIGN KEY ("repair_expense_account_id") REFERENCES "public"."accounts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_damage" ADD CONSTRAINT "asset_damage_repair_payment_account_id_accounts_id_fk" FOREIGN KEY ("repair_payment_account_id") REFERENCES "public"."accounts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assets" ADD CONSTRAINT "assets_asset_type_id_asset_types_id_fk" FOREIGN KEY ("asset_type_id") REFERENCES "public"."asset_types"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assets" ADD CONSTRAINT "assets_asset_sub_type_id_asset_types_id_fk" FOREIGN KEY ("asset_sub_type_id") REFERENCES "public"."asset_types"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "asset_damage_incident_at_index" ON "asset_damage" USING btree ("incident_at");--> statement-breakpoint
CREATE INDEX "asset_damage_repair_order_index" ON "asset_damage" USING btree ("repair_order_id");--> statement-breakpoint
CREATE INDEX "asset_damage_is_repaired_index" ON "asset_damage" USING btree ("is_repaired");--> statement-breakpoint
CREATE INDEX "asset_damage_repair_method_index" ON "asset_damage" USING btree ("repair_method");--> statement-breakpoint
CREATE INDEX "asset_damage_repaired_by_index" ON "asset_damage" USING btree ("repaired_by");--> statement-breakpoint
CREATE INDEX "asset_damage_repaired_at_index" ON "asset_damage" USING btree ("repaired_at");--> statement-breakpoint
CREATE INDEX "asset_damage_repair_expense_account_index" ON "asset_damage" USING btree ("repair_expense_account_id");--> statement-breakpoint
CREATE INDEX "asset_damage_repair_payment_account_index" ON "asset_damage" USING btree ("repair_payment_account_id");--> statement-breakpoint
CREATE INDEX "asset_damage_asset_severity_index" ON "asset_damage" USING btree ("asset_id","severity") WHERE "asset_damage"."is_deleted" = false;--> statement-breakpoint
CREATE INDEX "asset_damage_business_incident_at_index" ON "asset_damage" USING btree ("business_id","incident_at") WHERE "asset_damage"."is_deleted" = false;--> statement-breakpoint
CREATE INDEX "asset_damage_business_severity_index" ON "asset_damage" USING btree ("business_id","severity") WHERE "asset_damage"."is_deleted" = false;--> statement-breakpoint
CREATE INDEX "asset_damage_asset_category_index" ON "asset_damage" USING btree ("asset_id","damage_category") WHERE "asset_damage"."is_deleted" = false;--> statement-breakpoint
CREATE INDEX "asset_damage_unrepaired_index" ON "asset_damage" USING btree ("business_id","is_repaired","severity") WHERE "asset_damage"."is_deleted" = false;--> statement-breakpoint
CREATE INDEX "assets_asset_type_id_index" ON "assets" USING btree ("asset_type_id");--> statement-breakpoint
CREATE INDEX "assets_asset_sub_type_id_index" ON "assets" USING btree ("asset_sub_type_id");--> statement-breakpoint
CREATE INDEX "assets_business_asset_type_index" ON "assets" USING btree ("business_id","asset_type_id") WHERE "assets"."is_deleted" = false;--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "incident_date";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "incident_time";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "occurred_at";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "location_details";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "coordinates";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "repair_status";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "priority";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "repair_due_date";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "responsible_party_id";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "responsibility_type";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "is_insurance_claim";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "insurance_claim_number";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "is_warranty_covered";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "warranty_claim_number";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "estimated_repair_cost";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "labor_cost";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "parts_cost";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "attachment_ids";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "before_photo_ids";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "after_photo_ids";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "repair_vendor_id";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "repair_invoice_number";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "operational_impact";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "affected_functions";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "downtime";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key1";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key2";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key3";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key4";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key5";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key6";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key7";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key8";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key9";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "key10";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "is_fixed";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "fixed_date";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "fixed_by";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "checked_by";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "fixed_details";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "fixed_cost";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "approved_by";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "approved_at";--> statement-breakpoint
ALTER TABLE "asset_damage" DROP COLUMN "approval_notes";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "type";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key1";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key2";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key3";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key4";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key5";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key6";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key7";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key8";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key9";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "key10";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "category_id";--> statement-breakpoint
ALTER TABLE "assets" DROP COLUMN "sub_category_id";--> statement-breakpoint
DROP TYPE "public"."operational_impact";--> statement-breakpoint
DROP TYPE "public"."responsibility_type";