import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsBoolean,
  IsEnum,
  IsDateString,
  IsDecimal,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { RepairStatus, RepairPriority, RepairType } from '../../shared/types';
import { AmountType } from '../../drizzle/schema/expenses.schema';
import { MediaCollectionDto } from '../../shared/dto/media-association.dto';

export class UpdateAssetRepairOrderDto {
  @ApiPropertyOptional({ description: 'Asset ID being repaired' })
  @IsOptional()
  @IsUUID('4', { message: 'Asset ID must be a valid UUID' })
  assetId?: string;

  @ApiPropertyOptional({
    description: 'Title of the repair order',
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  title?: string;

  @ApiPropertyOptional({ description: 'Description of the problem' })
  @IsOptional()
  @IsString()
  problemDescription?: string;

  @ApiPropertyOptional({
    description: 'Diagnosis notes',
  })
  @IsOptional()
  @IsString()
  diagnosisNotes?: string;

  @ApiPropertyOptional({
    description: 'Repair solution description',
  })
  @IsOptional()
  @IsString()
  repairSolution?: string;

  @ApiPropertyOptional({
    description: 'Type of repair',
    enum: RepairType,
    enumName: 'RepairType',
    example: RepairType.CORRECTIVE,
  })
  @IsOptional()
  @IsEnum(RepairType, { message: 'Repair type must be a valid RepairType' })
  repairType?: RepairType;

  @ApiPropertyOptional({
    description: 'Current status of the repair order',
    enum: RepairStatus,
    enumName: 'RepairStatus',
    example: RepairStatus.REPORTED,
  })
  @IsOptional()
  @IsEnum(RepairStatus, { message: 'Status must be a valid RepairStatus' })
  status?: RepairStatus;

  @ApiPropertyOptional({
    description: 'Priority level of the repair',
    enum: RepairPriority,
    enumName: 'RepairPriority',
    example: RepairPriority.NORMAL,
  })
  @IsOptional()
  @IsEnum(RepairPriority, {
    message: 'Priority must be a valid RepairPriority',
  })
  priority?: RepairPriority;

  @ApiPropertyOptional({
    description: 'Target completion date',
    example: '2023-01-15T00:00:00Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Target completion date must be a valid date' })
  targetCompletionDate?: string;

  @ApiPropertyOptional({
    description: 'Actual start date of repair work',
    example: '2023-01-02T00:00:00Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Actual start date must be a valid date' })
  actualStartDate?: string;

  @ApiPropertyOptional({
    description: 'Actual completion date',
    example: '2023-01-10T00:00:00Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Completed at must be a valid date' })
  completedAt?: string;

  @ApiPropertyOptional({
    description: 'Completion notes',
  })
  @IsOptional()
  @IsString()
  completionNotes?: string;

  @ApiPropertyOptional({
    description: 'ID of staff member who reported the issue',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Reported by must be a valid UUID' })
  reportedBy?: string;

  @ApiPropertyOptional({
    description: 'ID of staff member assigned to the repair',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Assigned to must be a valid UUID' })
  assignedTo?: string;

  @ApiPropertyOptional({
    description: 'Supplier ID for external repairs',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Supplier ID must be a valid UUID' })
  supplierId?: string;

  @ApiPropertyOptional({
    description: 'Whether this is a warranty repair',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isWarrantyRepair?: boolean;

  @ApiPropertyOptional({
    description: 'Warranty claim number',
  })
  @IsOptional()
  @IsString()
  warrantyClaimNumber?: string;

  @ApiPropertyOptional({ description: 'Expense account ID' })
  @IsOptional()
  @IsUUID('4', { message: 'Expense account ID must be a valid UUID' })
  expenseAccountId?: string;

  @ApiPropertyOptional({
    description: 'Payment account ID',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Payment account ID must be a valid UUID' })
  paymentAccountId?: string;

  @ApiPropertyOptional({
    description: 'Payment date',
    example: '2023-01-10',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Payment date must be a valid date' })
  paymentDate?: string;

  @ApiPropertyOptional({
    description: 'Payment method ID',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Payment method ID must be a valid UUID' })
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'Payment reference number',
  })
  @IsOptional()
  @IsString()
  paymentReferenceNumber?: string;

  @ApiPropertyOptional({
    description: 'Amount calculation type',
    enum: AmountType,
    enumName: 'AmountType',
    example: AmountType.EXCLUSIVE_OF_TAX,
  })
  @IsOptional()
  @IsEnum(AmountType, { message: 'Amount type must be a valid AmountType' })
  amountType?: AmountType;

  @ApiPropertyOptional({
    description: 'Subtotal amount',
    example: '1000.00',
  })
  @IsOptional()
  @IsDecimal(
    { decimal_digits: '0,2' },
    { message: 'Subtotal must be a valid decimal' },
  )
  subtotal?: string;

  @ApiPropertyOptional({
    description: 'Total amount',
    example: '1100.00',
  })
  @IsOptional()
  @IsDecimal(
    { decimal_digits: '0,2' },
    { message: 'Total must be a valid decimal' },
  )
  total?: string;

  @ApiPropertyOptional({
    description: 'Tax ID',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Tax ID must be a valid UUID' })
  taxId?: string;

  @ApiPropertyOptional({
    description: 'Quality check performed by staff member ID',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Quality check by must be a valid UUID' })
  qualityCheckBy?: string;

  // Media association support (attachments)
  @ApiPropertyOptional({
    description: 'Media metadata for attachment uploads',
    type: MediaCollectionDto,
  })
  @IsOptional()
  @Type(() => MediaCollectionDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  })
  mediaMetadata?: MediaCollectionDto;

  @ApiPropertyOptional({
    description: 'Array of media association IDs to delete',
    type: [String],
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value.split(',').map((id: string) => id.trim());
      }
    }
    return value;
  })
  deleteMediaIds?: string[];

  @ApiPropertyOptional({
    description: 'Array of existing media associations to update',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        position: { type: 'number' },
        isPrimary: { type: 'boolean' },
        isVisible: { type: 'boolean' },
        metadata: { type: 'object' },
      },
    },
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return [];
      }
    }
    return value;
  })
  updateMediaAssociations?: Array<{
    id: string;
    position?: number;
    isPrimary?: boolean;
    isVisible?: boolean;
    metadata?: any;
  }>;
}
