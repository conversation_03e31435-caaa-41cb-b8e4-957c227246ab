CREATE TYPE "public"."currency_format" AS ENUM('<PERSON><PERSON><PERSON><PERSON>_PREFIX', 'D<PERSON><PERSON>R_SUFFIX', '<PERSON><PERSON><PERSON>R_SUFFIX_ALT', 'D<PERSON><PERSON>R_SPACE_PREFIX', '<PERSON><PERSON><PERSON><PERSON>_COMMA_PREFIX', '<PERSON><PERSON><PERSON>_DECIMAL');--> statement-breakpoint
CREATE TYPE "public"."date_format" AS ENUM('MM_DD_YYYY', 'DD_MM_YYYY', 'DD_MMM_YYYY', 'YYYY_MM_DD', 'YYYY_MMM_DD', 'MM_DD_YYYY_ALT', 'DD_MM_YYYY_ALT', 'MM_DD_YYYY_DOT', 'YYYY_MM_DD_DOT');--> statement-breakpoint
CREATE TYPE "public"."document_type" AS ENUM('INVOICE', 'SALES_ORDER', 'QUOTATION', 'SALES_RECEIPT', 'CREDIT_NOTE', 'PUR<PERSON><PERSON>E_ORDER', 'BILL', 'DEBIT_NOTE', 'PAYMENT_VOUCHER');--> statement-breakpoint
CREATE TYPE "public"."sequence_type" AS ENUM('NUMERIC', 'ALPHABETIC_UPPER', 'ALPHABETIC_LOWER', 'ALPHANUMERIC');--> statement-breakpoint
CREATE TYPE "public"."time_format" AS ENUM('HH12_MM_AM_PM', 'HH12_MM_SS_AM_PM', 'HH24_MM', 'HH24_MM_SS', 'ISO_TIME');--> statement-breakpoint
CREATE TABLE "business_document_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"business_id" uuid NOT NULL,
	"date_format" date_format DEFAULT 'MM_DD_YYYY' NOT NULL,
	"time_format" time_format DEFAULT 'HH12_MM_AM_PM' NOT NULL,
	"currency_format" "currency_format" DEFAULT 'DOLLAR_PREFIX' NOT NULL,
	"timezone" varchar(100) DEFAULT 'UTC' NOT NULL,
	"locale" varchar(10) DEFAULT 'en-US' NOT NULL,
	"fiscal_year_start" jsonb DEFAULT '{"month":1,"day":1}'::jsonb NOT NULL,
	"created_by" uuid NOT NULL,
	"updated_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	CONSTRAINT "business_document_settings_business_id_unique" UNIQUE("business_id")
);
--> statement-breakpoint
CREATE TABLE "document_formats" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"business_id" uuid NOT NULL,
	"document_type" "document_type" NOT NULL,
	"prefix" varchar(20) NOT NULL,
	"number_pattern" varchar(50) NOT NULL,
	"sequence_start" integer DEFAULT 1 NOT NULL,
	"sequence_increment" integer DEFAULT 1 NOT NULL,
	"sequence_type" "sequence_type" DEFAULT 'NUMERIC' NOT NULL,
	"sequence_start_alpha" varchar(10) DEFAULT 'A',
	"sequence_min_length" integer DEFAULT 1,
	"reset_sequence" varchar(20) DEFAULT 'never',
	"last_reset_date" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"metadata" jsonb DEFAULT '{}'::jsonb,
	"created_by" uuid NOT NULL,
	"updated_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	CONSTRAINT "document_formats_business_id_document_type_unique" UNIQUE("business_id","document_type")
);
--> statement-breakpoint
CREATE TABLE "document_sequences" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"business_id" uuid NOT NULL,
	"document_type" "document_type" NOT NULL,
	"year" integer NOT NULL,
	"month" integer,
	"current_sequence" integer DEFAULT 0 NOT NULL,
	"current_sequence_alpha" varchar(10) DEFAULT '',
	"last_used_at" timestamp,
	"created_by" uuid NOT NULL,
	"updated_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	CONSTRAINT "document_sequences_business_id_document_type_year_month_unique" UNIQUE("business_id","document_type","year","month")
);
--> statement-breakpoint
ALTER TABLE "business_document_settings" ADD CONSTRAINT "business_document_settings_business_id_business_id_fk" FOREIGN KEY ("business_id") REFERENCES "public"."business"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "business_document_settings" ADD CONSTRAINT "business_document_settings_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "business_document_settings" ADD CONSTRAINT "business_document_settings_updated_by_users_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "document_formats" ADD CONSTRAINT "document_formats_business_id_business_id_fk" FOREIGN KEY ("business_id") REFERENCES "public"."business"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "document_formats" ADD CONSTRAINT "document_formats_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "document_formats" ADD CONSTRAINT "document_formats_updated_by_users_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "document_sequences" ADD CONSTRAINT "document_sequences_business_id_business_id_fk" FOREIGN KEY ("business_id") REFERENCES "public"."business"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "document_sequences" ADD CONSTRAINT "document_sequences_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "document_sequences" ADD CONSTRAINT "document_sequences_updated_by_users_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "doc_fmt_business_id_idx" ON "document_formats" USING btree ("business_id");--> statement-breakpoint
CREATE INDEX "doc_fmt_doc_type_idx" ON "document_formats" USING btree ("document_type");--> statement-breakpoint
CREATE INDEX "doc_seq_idx" ON "document_sequences" USING btree ("business_id","document_type","year");