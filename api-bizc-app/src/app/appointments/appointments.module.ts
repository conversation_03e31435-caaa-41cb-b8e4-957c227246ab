import { Module } from '@nestjs/common';
import { AppointmentsService } from './appointments.service';
import { AppointmentsController } from './appointments.controller';
import { AuthModule } from '../auth/auth.module';
import { DrizzleModule } from '../drizzle/drizzle.module';
import { ActivityLogModule } from '../activity-log/activity-log.module';
import { SharedServicesModule } from '../shared/services/shared-services.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    AuthModule,
    DrizzleModule,
    ActivityLogModule,
    UsersModule,
    SharedServicesModule,
  ],
  controllers: [AppointmentsController],
  providers: [AppointmentsService],
  exports: [AppointmentsService],
})
export class AppointmentsModule {}
