import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  DamageCategory,
  DamageSeverity,
} from '../../drizzle/schema/asset-damage.schema';

export class AssetDamageListDto {
  @ApiProperty({
    description: 'Unique identifier for the vehicle damage record',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Asset ID that has the damage',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  assetId: string;

  @ApiPropertyOptional({
    description: 'Asset display name',
    example: 'Toyota Camry 2020',
  })
  assetName?: string;

  @ApiProperty({
    description: 'Type of damage',
    example: 'Scratch',
  })
  damageType: string;

  @ApiProperty({
    description: 'Damage category enum',
    enum: DamageCategory,
    example: DamageCategory.PHYSICAL,
  })
  damageCategory: DamageCategory;

  @ApiProperty({
    description: 'Damage severity enum',
    enum: DamageSeverity,
    example: DamageSeverity.MINOR,
  })
  severity: DamageSeverity;

  @ApiProperty({
    description: 'Estimated cost of damage',
    example: '500.00',
  })
  amount: string;

  @ApiProperty({
    description: 'Date and time when the incident occurred',
    example: '2024-01-15T14:30:00Z',
  })
  incidentAt: string;

  @ApiProperty({
    description: 'Whether the damage has been repaired',
    example: false,
  })
  isRepaired: boolean;

  @ApiPropertyOptional({
    description: 'Timestamp when the damage was repaired',
    example: '2024-01-20T10:00:00Z',
  })
  repairedAt?: string;

  @ApiPropertyOptional({
    description: 'Cost of the repair work',
    example: '450.00',
  })
  repairCost?: string;
}
