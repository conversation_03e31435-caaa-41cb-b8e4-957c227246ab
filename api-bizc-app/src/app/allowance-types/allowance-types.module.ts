import { Module } from '@nestjs/common';
import { AllowanceTypesService } from './allowance-types.service';
import { AllowanceTypesController } from './allowance-types.controller';
import { AuthModule } from '../auth/auth.module';
import { DrizzleModule } from '../drizzle/drizzle.module';
import { ActivityLogModule } from '../activity-log/activity-log.module';
import { SharedServicesModule } from '../shared/services/shared-services.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    AuthModule,
    DrizzleModule,
    ActivityLogModule,
    UsersModule,
    SharedServicesModule,
  ],
  controllers: [AllowanceTypesController],
  providers: [AllowanceTypesService],
  exports: [AllowanceTypesService],
})
export class AllowanceTypesModule {}
